/**
 * @fileoverview DND Persistence Configuration
 * @description Configuration constants and settings for DND validation persistence operations
 * <AUTHOR> System
 * @version 1.0.0
 */

import { DNDPersistenceConfig } from '../types';

/**
 * Default configuration for DND persistence operations
 * @constant {DNDPersistenceConfig}
 */
export const DEFAULT_DND_PERSISTENCE_CONFIG: DNDPersistenceConfig = {
  // Batch processing settings
  defaultBatchSize: 100,
  maxBatchSize: 1000,
  
  // Retry configuration
  retryAttempts: 3,
  retryDelayMs: 1000,
  
  // Database connection settings
  connectionTimeout: 5000,
  queryTimeout: 30000,
  
  // Validation caching
  recentValidationHours: 24,
  
  // Monitoring and logging
  auditLogging: true,
  performanceMonitoring: true,
  
  // Rate limiting configuration
  rateLimiting: {
    enabled: true,
    maxRequestsPerMinute: 100,
    maxRequestsPerHour: 1000
  },
  
  // Data cleanup configuration
  cleanup: {
    enabled: true,
    retentionDays: 90,
    batchSize: 500
  }
};

/**
 * SQL query constants for DND persistence operations
 * @constant
 */
export const DND_PERSISTENCE_QUERIES = {
  // Basic CRUD operations
  FIND_BY_PHONE: `
    SELECT phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
    FROM dnd_validations 
    WHERE phone_number = $1
  `,
  
  UPSERT_VALIDATION: `
    INSERT INTO dnd_validations (phone_number, dnd_status, validated_at, validation_metadata)
    VALUES ($1, $2, $3, $4)
    ON CONFLICT (phone_number) 
    DO UPDATE SET 
      dnd_status = EXCLUDED.dnd_status,
      validated_at = EXCLUDED.validated_at,
      validation_metadata = EXCLUDED.validation_metadata,
      updated_at = CURRENT_TIMESTAMP
    RETURNING phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
  `,
  
  DELETE_VALIDATION: `
    DELETE FROM dnd_validations WHERE phone_number = $1
  `,
  
  // Statistics and reporting
  GET_STATS: `
    SELECT 
      COUNT(*) as total_validations,
      COUNT(CASE WHEN dnd_status = 'DND' THEN 1 END) as dnd_count,
      COUNT(CASE WHEN dnd_status = 'Non-DND' THEN 1 END) as non_dnd_count,
      COUNT(CASE WHEN dnd_status = 'Error' THEN 1 END) as error_count,
      MAX(validated_at) as last_validated_at
    FROM dnd_validations
  `,
  
  // Recent validation check
  IS_RECENTLY_VALIDATED: `
    SELECT 1 FROM dnd_validations 
    WHERE phone_number = $1 
    AND validated_at > NOW() - INTERVAL '%s hours'
  `,
  
  // Cleanup operations
  CLEANUP_OLD_VALIDATIONS: `
    DELETE FROM dnd_validations 
    WHERE validated_at < NOW() - INTERVAL '%s days'
    AND phone_number IN (
      SELECT phone_number FROM dnd_validations 
      WHERE validated_at < NOW() - INTERVAL '%s days'
      LIMIT $1
    )
  `,
  
  // Health check
  HEALTH_CHECK: `
    SELECT 
      COUNT(*) as total_records,
      COUNT(CASE WHEN validated_at > NOW() - INTERVAL '24 hours' THEN 1 END) as recent_records,
      pg_database_size(current_database()) as database_size
    FROM dnd_validations
  `
} as const;

/**
 * Database table and column constants
 * @constant
 */
export const DND_PERSISTENCE_SCHEMA = {
  TABLE_NAME: 'dnd_validations',
  COLUMNS: {
    PHONE_NUMBER: 'phone_number',
    DND_STATUS: 'dnd_status',
    VALIDATED_AT: 'validated_at',
    VALIDATION_METADATA: 'validation_metadata',
    CREATED_AT: 'created_at',
    UPDATED_AT: 'updated_at'
  },
  INDEXES: {
    PRIMARY_KEY: 'dnd_validations_pkey',
    DND_STATUS_IDX: 'idx_dnd_validations_dnd_status',
    VALIDATED_AT_IDX: 'idx_dnd_validations_validated_at',
    CREATED_AT_IDX: 'idx_dnd_validations_created_at',
    METADATA_GIN_IDX: 'idx_dnd_validations_metadata_gin'
  }
} as const;

/**
 * Validation constants and patterns
 * @constant
 */
export const DND_VALIDATION_CONSTANTS = {
  // Phone number validation
  PHONE_NUMBER: {
    MIN_LENGTH: 10,
    MAX_LENGTH: 15,
    INDIAN_MOBILE_PATTERN: /^[6-9]\d{9}$/,
    INTERNATIONAL_PATTERN: /^\+[1-9]\d{1,14}$/
  },
  
  // DND status values
  DND_STATUS: {
    DND: 'DND' as const,
    NON_DND: 'Non-DND' as const,
    ERROR: 'Error' as const
  },
  
  // Metadata limits
  METADATA: {
    MAX_SIZE_BYTES: 1024 * 10, // 10KB
    MAX_KEYS: 50
  },
  
  // Performance thresholds
  PERFORMANCE: {
    SLOW_QUERY_THRESHOLD_MS: 1000,
    BULK_OPERATION_WARNING_SIZE: 5000,
    MAX_CONCURRENT_OPERATIONS: 10
  }
} as const;

/**
 * Error codes for DND persistence operations
 * @constant
 */
export const DND_PERSISTENCE_ERROR_CODES = {
  // Validation errors
  INVALID_PHONE_NUMBER: 'DND_INVALID_PHONE_NUMBER',
  INVALID_DND_STATUS: 'DND_INVALID_DND_STATUS',
  INVALID_METADATA: 'DND_INVALID_METADATA',
  
  // Database errors
  CONNECTION_FAILED: 'DND_CONNECTION_FAILED',
  QUERY_TIMEOUT: 'DND_QUERY_TIMEOUT',
  TRANSACTION_FAILED: 'DND_TRANSACTION_FAILED',
  
  // Business logic errors
  VALIDATION_NOT_FOUND: 'DND_VALIDATION_NOT_FOUND',
  DUPLICATE_VALIDATION: 'DND_DUPLICATE_VALIDATION',
  RECENT_VALIDATION_EXISTS: 'DND_RECENT_VALIDATION_EXISTS',
  
  // Rate limiting errors
  RATE_LIMIT_EXCEEDED: 'DND_RATE_LIMIT_EXCEEDED',
  BULK_SIZE_EXCEEDED: 'DND_BULK_SIZE_EXCEEDED',
  
  // System errors
  INTERNAL_ERROR: 'DND_INTERNAL_ERROR',
  CONFIGURATION_ERROR: 'DND_CONFIGURATION_ERROR'
} as const;

/**
 * Audit log event types
 * @constant
 */
export const DND_AUDIT_EVENTS = {
  VALIDATION_CREATED: 'validation_created',
  VALIDATION_UPDATED: 'validation_updated',
  VALIDATION_DELETED: 'validation_deleted',
  BULK_VALIDATION_CREATED: 'bulk_validation_created',
  CLEANUP_EXECUTED: 'cleanup_executed',
  HEALTH_CHECK_PERFORMED: 'health_check_performed'
} as const;

/**
 * Get environment-specific configuration
 * @param {string} environment - The environment name (development, production, test)
 * @returns {Partial<DNDPersistenceConfig>} Environment-specific configuration overrides
 */
export function getEnvironmentConfig(environment: string): Partial<DNDPersistenceConfig> {
  switch (environment) {
    case 'production':
      return {
        retryAttempts: 5,
        retryDelayMs: 2000,
        rateLimiting: {
          enabled: true,
          maxRequestsPerMinute: 200,
          maxRequestsPerHour: 5000
        },
        cleanup: {
          enabled: true,
          retentionDays: 180,
          batchSize: 1000
        }
      };
      
    case 'test':
      return {
        retryAttempts: 1,
        retryDelayMs: 100,
        auditLogging: false,
        performanceMonitoring: false,
        rateLimiting: {
          enabled: false,
          maxRequestsPerMinute: 1000,
          maxRequestsPerHour: 10000
        },
        cleanup: {
          enabled: false,
          retentionDays: 1,
          batchSize: 100
        }
      };
      
    case 'development':
    default:
      return {
        retryAttempts: 2,
        retryDelayMs: 500,
        performanceMonitoring: true,
        rateLimiting: {
          enabled: false,
          maxRequestsPerMinute: 500,
          maxRequestsPerHour: 2000
        }
      };
  }
}

/**
 * Merge default configuration with environment-specific overrides
 * @param {string} environment - The environment name
 * @returns {DNDPersistenceConfig} Complete configuration object
 */
export function createDNDPersistenceConfig(environment: string = 'development'): DNDPersistenceConfig {
  const envConfig = getEnvironmentConfig(environment);
  return {
    ...DEFAULT_DND_PERSISTENCE_CONFIG,
    ...envConfig,
    rateLimiting: {
      ...DEFAULT_DND_PERSISTENCE_CONFIG.rateLimiting,
      ...envConfig.rateLimiting
    },
    cleanup: {
      ...DEFAULT_DND_PERSISTENCE_CONFIG.cleanup,
      ...envConfig.cleanup
    }
  };
}
