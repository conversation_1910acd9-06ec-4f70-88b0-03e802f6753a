import { DNDValidationModel } from '@/database/models/DNDValidation';
import { db } from '@/database/connection';
import { CreateDNDValidationRequest, UpdateDNDValidationRequest } from '@/types';

describe('DNDValidationModel', () => {
  beforeAll(async () => {
    await db.connect();
  });

  afterAll(async () => {
    await db.disconnect();
  });

  afterAll(async () => {
    // Clean up test data
    try {
      await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['test%']);
      await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['stats%']);
      await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['bulk%']);
    } catch (error) {
      console.log('Cleanup error (expected if database already disconnected):', error);
    }
    await db.disconnect();
  });

  beforeEach(async () => {
    // Clear any existing test data
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['test%']);
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['stats%']);
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['bulk%']);
  });

  describe('upsert', () => {
    it('should create a new DND validation record', async () => {
      const validationData: CreateDNDValidationRequest = {
        phoneNumber: 'test9876543210',
        dndStatus: 'DND',
        validationMetadata: {
          source: 'test',
          originalPhone: '+919876543210'
        }
      };

      const result = await DNDValidationModel.upsert(validationData);

      expect(result.phoneNumber).toBe('test9876543210');
      expect(result.dndStatus).toBe('DND');
      expect(result.validationMetadata).toEqual(validationData.validationMetadata);
      expect(result.createdAt).toBeDefined();
      expect(result.updatedAt).toBeDefined();
    });

    it('should update existing DND validation record on conflict', async () => {
      const phoneNumber = 'test9876543211';
      
      // Create initial record
      const initialData: CreateDNDValidationRequest = {
        phoneNumber,
        dndStatus: 'Non-DND',
        validationMetadata: { source: 'initial' }
      };
      
      const initial = await DNDValidationModel.upsert(initialData);
      
      // Update with new data
      const updateData: CreateDNDValidationRequest = {
        phoneNumber,
        dndStatus: 'DND',
        validationMetadata: { source: 'updated' }
      };
      
      const updated = await DNDValidationModel.upsert(updateData);

      expect(updated.phoneNumber).toBe(phoneNumber);
      expect(updated.dndStatus).toBe('DND');
      expect(updated.validationMetadata).toEqual({ source: 'updated' });
      expect(updated.createdAt).toEqual(initial.createdAt);
      expect(updated.updatedAt.getTime()).toBeGreaterThan(initial.updatedAt.getTime());
    });

    it('should handle validation metadata as empty object when not provided', async () => {
      const validationData: CreateDNDValidationRequest = {
        phoneNumber: 'test9876543212',
        dndStatus: 'Error'
      };

      const result = await DNDValidationModel.upsert(validationData);

      expect(result.validationMetadata).toEqual({});
    });
  });

  describe('findByPhone', () => {
    it('should find existing DND validation by phone number', async () => {
      const phoneNumber = 'test9876543213';
      const validationData: CreateDNDValidationRequest = {
        phoneNumber,
        dndStatus: 'Non-DND',
        validationMetadata: { test: 'data' }
      };

      await DNDValidationModel.upsert(validationData);
      const found = await DNDValidationModel.findByPhone(phoneNumber);

      expect(found).not.toBeNull();
      expect(found!.phoneNumber).toBe(phoneNumber);
      expect(found!.dndStatus).toBe('Non-DND');
      expect(found!.validationMetadata).toEqual({ test: 'data' });
    });

    it('should return null for non-existent phone number', async () => {
      const found = await DNDValidationModel.findByPhone('nonexistent');
      expect(found).toBeNull();
    });
  });

  describe('findAll', () => {
    beforeEach(async () => {
      // Create test data
      const testData: CreateDNDValidationRequest[] = [
        { phoneNumber: 'test1111111111', dndStatus: 'DND' },
        { phoneNumber: 'test2222222222', dndStatus: 'Non-DND' },
        { phoneNumber: 'test3333333333', dndStatus: 'Error' },
        { phoneNumber: 'test4444444444', dndStatus: 'DND' }
      ];

      for (const data of testData) {
        await DNDValidationModel.upsert(data);
      }
    });

    it('should return all validations with pagination', async () => {
      const result = await DNDValidationModel.findAll({ page: 1, limit: 2 });

      expect(result.validations).toHaveLength(2);
      expect(result.total).toBe(4);
    });

    it('should filter by DND status', async () => {
      const result = await DNDValidationModel.findAll({ dndStatus: 'DND' });

      expect(result.validations).toHaveLength(2);
      expect(result.validations.every(v => v.dndStatus === 'DND')).toBe(true);
    });

    it('should search by phone number', async () => {
      const result = await DNDValidationModel.findAll({ search: '1111' });

      expect(result.validations).toHaveLength(1);
      expect(result.validations[0]?.phoneNumber).toBe('test1111111111');
    });
  });

  describe('bulkUpsert', () => {
    it('should insert multiple validation records', async () => {
      const validations: CreateDNDValidationRequest[] = [
        { phoneNumber: 'bulk1111111111', dndStatus: 'DND' },
        { phoneNumber: 'bulk2222222222', dndStatus: 'Non-DND' },
        { phoneNumber: 'bulk3333333333', dndStatus: 'Error' }
      ];

      const results = await DNDValidationModel.bulkUpsert(validations);

      expect(results).toHaveLength(3);
      expect(results.map(r => r.phoneNumber)).toEqual([
        'bulk1111111111',
        'bulk2222222222', 
        'bulk3333333333'
      ]);
    });

    it('should handle empty array', async () => {
      const results = await DNDValidationModel.bulkUpsert([]);
      expect(results).toHaveLength(0);
    });

    it('should update existing records in bulk operation', async () => {
      const phoneNumber = 'bulk4444444444';
      
      // Create initial record
      await DNDValidationModel.upsert({
        phoneNumber,
        dndStatus: 'Non-DND'
      });

      // Bulk upsert with update
      const validations: CreateDNDValidationRequest[] = [
        { phoneNumber, dndStatus: 'DND' },
        { phoneNumber: 'bulk5555555555', dndStatus: 'Non-DND' }
      ];

      const results = await DNDValidationModel.bulkUpsert(validations);

      expect(results).toHaveLength(2);
      const updated = results.find(r => r.phoneNumber === phoneNumber);
      expect(updated?.dndStatus).toBe('DND');
    });
  });

  describe('update', () => {
    it('should update existing validation record', async () => {
      const phoneNumber = 'test5555555555';
      
      // Create initial record
      await DNDValidationModel.upsert({
        phoneNumber,
        dndStatus: 'Non-DND',
        validationMetadata: { initial: true }
      });

      // Update the record
      const updateData: UpdateDNDValidationRequest = {
        dndStatus: 'DND',
        validationMetadata: { updated: true }
      };

      const updated = await DNDValidationModel.update(phoneNumber, updateData);

      expect(updated.dndStatus).toBe('DND');
      expect(updated.validationMetadata).toEqual({ updated: true });
    });

    it('should throw error for non-existent record', async () => {
      const updateData: UpdateDNDValidationRequest = {
        dndStatus: 'DND'
      };

      await expect(
        DNDValidationModel.update('nonexistent', updateData)
      ).rejects.toThrow('DND validation not found');
    });

    it('should return unchanged record when no update fields provided', async () => {
      const phoneNumber = 'test6666666666';
      
      const original = await DNDValidationModel.upsert({
        phoneNumber,
        dndStatus: 'Non-DND'
      });

      const unchanged = await DNDValidationModel.update(phoneNumber, {});

      expect(unchanged).toEqual(original);
    });
  });

  describe('delete', () => {
    it('should delete existing validation record', async () => {
      const phoneNumber = 'test7777777777';
      
      await DNDValidationModel.upsert({
        phoneNumber,
        dndStatus: 'DND'
      });

      await DNDValidationModel.delete(phoneNumber);

      const found = await DNDValidationModel.findByPhone(phoneNumber);
      expect(found).toBeNull();
    });

    it('should throw error for non-existent record', async () => {
      await expect(
        DNDValidationModel.delete('nonexistent')
      ).rejects.toThrow('DND validation not found');
    });
  });

  describe('getStats', () => {
    beforeEach(async () => {
      // Create test data with known distribution
      const testData: CreateDNDValidationRequest[] = [
        { phoneNumber: 'stats1111111111', dndStatus: 'DND' },
        { phoneNumber: 'stats2222222222', dndStatus: 'DND' },
        { phoneNumber: 'stats3333333333', dndStatus: 'Non-DND' },
        { phoneNumber: 'stats4444444444', dndStatus: 'Error' }
      ];

      for (const data of testData) {
        await DNDValidationModel.upsert(data);
      }
    });

    it('should return correct validation statistics', async () => {
      const stats = await DNDValidationModel.getStats();

      expect(stats.totalValidations).toBe(4);
      expect(stats.dndCount).toBe(2);
      expect(stats.nonDndCount).toBe(1);
      expect(stats.errorCount).toBe(1);
      expect(stats.lastValidatedAt).toBeDefined();
    });
  });

  describe('isRecentlyValidated', () => {
    it('should return true for recently validated phone number', async () => {
      const phoneNumber = 'test8888888888';
      
      await DNDValidationModel.upsert({
        phoneNumber,
        dndStatus: 'DND',
        validatedAt: new Date()
      });

      const isRecent = await DNDValidationModel.isRecentlyValidated(phoneNumber, 24);
      expect(isRecent).toBe(true);
    });

    it('should return false for non-existent phone number', async () => {
      const isRecent = await DNDValidationModel.isRecentlyValidated('nonexistent', 24);
      expect(isRecent).toBe(false);
    });

    it('should return false for old validation outside time window', async () => {
      const phoneNumber = 'test9999999999';
      
      // Create validation with old timestamp
      const oldDate = new Date();
      oldDate.setDate(oldDate.getDate() - 30); // 30 days ago
      
      await DNDValidationModel.upsert({
        phoneNumber,
        dndStatus: 'DND',
        validatedAt: oldDate
      });

      const isRecent = await DNDValidationModel.isRecentlyValidated(phoneNumber, 24);
      expect(isRecent).toBe(false);
    });
  });
});
