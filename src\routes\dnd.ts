import { Router, Request, Response } from 'express';
import DNDController from '../controllers/dndController';
import { csvUploadMiddleware } from '../middleware/upload';
import logger from '../utils/logger';

const router = Router();
const dndController = new DNDController();

/**
 * POST /api/dnd/upload
 * Upload CSV file for bulk DND validation
 * 
 * @body {file} csvFile - CSV file with columns: name, phone, email
 * @returns {object} Validation results with DND status for each record
 */
router.post('/upload',
  ...csvUploadMiddleware,
  async (req: any, res: any) => {
    try {
      await dndController.uploadAndValidate(req, res);
    } catch (error) {
      logger.error('DND upload route error:', error);
      res.status(500).json({
        success: false,
        error: 'Internal server error'
      });
    }
  }
);

/**
 * GET /api/dnd/health
 * Check DND service health status
 * 
 * @returns {object} Service health information
 */
router.get('/health', async (req: any, res: any) => {
  try {
    await dndController.getHealthStatus(req, res);
  } catch (error) {
    logger.error('DND health route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * GET /api/dnd/config
 * Get service configuration and limits
 *
 * @returns {object} Service configuration details
 */
router.get('/config', async (req: any, res: any) => {
  try {
    await dndController.getConfiguration(req, res);
  } catch (error) {
    logger.error('DND config route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

/**
 * POST /api/dnd/validate-single
 * Validate single phone number (for testing)
 *
 * @body {string} phone - Phone number to validate
 * @returns {object} DND status for the phone number
 */
router.post('/validate-single', async (req: any, res: any) => {
  try {
    await dndController.validateSingle(req, res);
  } catch (error) {
    logger.error('DND single validation route error:', error);
    res.status(500).json({
      success: false,
      error: 'Internal server error'
    });
  }
});

export default router;
