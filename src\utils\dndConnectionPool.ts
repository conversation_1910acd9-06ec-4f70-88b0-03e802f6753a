/**
 * @fileoverview Database Connection Pool Manager for DND Operations
 * @description Enhanced connection pooling with monitoring and optimization for DND persistence
 * <AUTHOR> System
 * @version 1.0.0
 */

import { Pool, PoolClient, PoolConfig } from 'pg';
import { createDNDPersistenceConfig } from '../config/dndPersistence';
import logger from './logger';

/**
 * Connection pool statistics
 */
export interface PoolStats {
  totalConnections: number;
  idleConnections: number;
  waitingClients: number;
  totalQueries: number;
  averageQueryTime: number;
  connectionErrors: number;
  lastError?: string;
}

/**
 * Connection pool configuration
 */
export interface DNDPoolConfig extends PoolConfig {
  monitoringInterval?: number;
  slowQueryThreshold?: number;
  connectionHealthCheck?: boolean;
  healthCheckInterval?: number;
}

/**
 * Enhanced database connection pool manager for DND operations
 */
export class DNDConnectionPoolManager {
  private pool: Pool;
  private config: ReturnType<typeof createDNDPersistenceConfig>;
  private stats: PoolStats;
  private monitoringInterval?: NodeJS.Timeout;
  private healthCheckInterval?: NodeJS.Timeout;
  private queryTimes: number[] = [];
  private readonly maxQueryTimeHistory = 1000;

  /**
   * Creates a new connection pool manager
   * @param {DNDPoolConfig} poolConfig - Pool configuration
   * @param {string} environment - Environment configuration
   */
  constructor(
    poolConfig: DNDPoolConfig = {},
    environment: string = process.env.NODE_ENV || 'development'
  ) {
    this.config = createDNDPersistenceConfig(environment);
    
    const defaultPoolConfig: DNDPoolConfig = {
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      database: process.env.DB_NAME || 'teleai',
      user: process.env.DB_USER || 'postgres',
      password: process.env.DB_PASSWORD || '',
      max: 20, // Maximum number of connections
      min: 5,  // Minimum number of connections
      idleTimeoutMillis: 30000, // 30 seconds
      connectionTimeoutMillis: this.config.connectionTimeout,
      query_timeout: this.config.queryTimeout,
      keepAlive: true,
      keepAliveInitialDelayMillis: 10000,
      monitoringInterval: 60000, // 1 minute
      slowQueryThreshold: this.config.performanceMonitoring ? 1000 : 5000,
      connectionHealthCheck: true,
      healthCheckInterval: 300000 // 5 minutes
    };

    const finalConfig = { ...defaultPoolConfig, ...poolConfig };
    
    this.pool = new Pool(finalConfig);
    this.stats = {
      totalConnections: 0,
      idleConnections: 0,
      waitingClients: 0,
      totalQueries: 0,
      averageQueryTime: 0,
      connectionErrors: 0
    };

    this.setupEventHandlers();
    
    if (finalConfig.monitoringInterval) {
      this.startMonitoring(finalConfig.monitoringInterval);
    }
    
    if (finalConfig.connectionHealthCheck && finalConfig.healthCheckInterval) {
      this.startHealthCheck(finalConfig.healthCheckInterval);
    }

    logger.info('DND Connection Pool initialized', {
      max: finalConfig.max,
      min: finalConfig.min,
      host: finalConfig.host,
      database: finalConfig.database
    });
  }

  /**
   * Executes a query with connection pooling and monitoring
   * @param {string} text - SQL query text
   * @param {any[]} params - Query parameters
   * @returns {Promise<any>} Query result
   */
  async query(text: string, params?: any[]): Promise<any> {
    const startTime = Date.now();
    let client: PoolClient | null = null;
    
    try {
      client = await this.pool.connect();
      const result = await client.query(text, params);
      
      const queryTime = Date.now() - startTime;
      this.recordQueryTime(queryTime);
      this.stats.totalQueries++;
      
      if (queryTime > (this.pool.options as DNDPoolConfig).slowQueryThreshold!) {
        logger.warn('Slow query detected', {
          queryTime,
          query: text.substring(0, 100),
          params: params?.length
        });
      }
      
      return result;
      
    } catch (error) {
      const queryTime = Date.now() - startTime;
      this.stats.connectionErrors++;
      this.stats.lastError = error instanceof Error ? error.message : String(error);
      
      logger.error('Database query failed', {
        queryTime,
        query: text.substring(0, 100),
        params: params?.length,
        error: this.stats.lastError
      });
      
      throw error;
    } finally {
      if (client) {
        client.release();
      }
    }
  }

  /**
   * Gets a client from the pool for transaction use
   * @returns {Promise<PoolClient>} Database client
   */
  async getClient(): Promise<PoolClient> {
    try {
      return await this.pool.connect();
    } catch (error) {
      this.stats.connectionErrors++;
      this.stats.lastError = error instanceof Error ? error.message : String(error);
      
      logger.error('Failed to get database client', {
        error: this.stats.lastError
      });
      
      throw error;
    }
  }

  /**
   * Gets current pool statistics
   * @returns {PoolStats} Pool statistics
   */
  getStats(): PoolStats {
    return {
      ...this.stats,
      totalConnections: this.pool.totalCount,
      idleConnections: this.pool.idleCount,
      waitingClients: this.pool.waitingCount,
      averageQueryTime: this.calculateAverageQueryTime()
    };
  }

  /**
   * Performs health check on the connection pool
   * @returns {Promise<boolean>} True if healthy
   */
  async healthCheck(): Promise<boolean> {
    try {
      const client = await this.pool.connect();
      await client.query('SELECT 1');
      client.release();
      return true;
    } catch (error) {
      logger.error('Connection pool health check failed', { error });
      return false;
    }
  }

  /**
   * Gracefully closes the connection pool
   * @returns {Promise<void>}
   */
  async close(): Promise<void> {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    await this.pool.end();
    logger.info('DND Connection Pool closed');
  }

  /**
   * Sets up event handlers for the pool
   * @private
   */
  private setupEventHandlers(): void {
    this.pool.on('connect', (client) => {
      logger.debug('New client connected to pool');
    });

    this.pool.on('acquire', (client) => {
      logger.debug('Client acquired from pool');
    });

    this.pool.on('remove', (client) => {
      logger.debug('Client removed from pool');
    });

    this.pool.on('error', (error, client) => {
      this.stats.connectionErrors++;
      this.stats.lastError = error.message;
      
      logger.error('Pool error occurred', {
        error: error.message,
        clientProcessId: client?.processID
      });
    });
  }

  /**
   * Starts monitoring the connection pool
   * @private
   * @param {number} interval - Monitoring interval in milliseconds
   */
  private startMonitoring(interval: number): void {
    this.monitoringInterval = setInterval(() => {
      const stats = this.getStats();
      
      logger.info('Connection pool stats', stats);
      
      // Alert on high connection usage
      if (stats.totalConnections > (this.pool.options.max! * 0.8)) {
        logger.warn('High connection pool usage', {
          usage: `${stats.totalConnections}/${this.pool.options.max}`,
          waitingClients: stats.waitingClients
        });
      }
      
      // Alert on high error rate
      if (stats.connectionErrors > 10) {
        logger.warn('High connection error rate', {
          errors: stats.connectionErrors,
          lastError: stats.lastError
        });
      }
      
    }, interval);
  }

  /**
   * Starts periodic health checks
   * @private
   * @param {number} interval - Health check interval in milliseconds
   */
  private startHealthCheck(interval: number): void {
    this.healthCheckInterval = setInterval(async () => {
      const isHealthy = await this.healthCheck();
      
      if (!isHealthy) {
        logger.error('Connection pool health check failed');
      } else {
        logger.debug('Connection pool health check passed');
      }
    }, interval);
  }

  /**
   * Records query execution time
   * @private
   * @param {number} queryTime - Query execution time in milliseconds
   */
  private recordQueryTime(queryTime: number): void {
    this.queryTimes.push(queryTime);
    
    // Keep only recent query times
    if (this.queryTimes.length > this.maxQueryTimeHistory) {
      this.queryTimes.shift();
    }
  }

  /**
   * Calculates average query time
   * @private
   * @returns {number} Average query time in milliseconds
   */
  private calculateAverageQueryTime(): number {
    if (this.queryTimes.length === 0) {
      return 0;
    }
    
    const sum = this.queryTimes.reduce((acc, time) => acc + time, 0);
    return sum / this.queryTimes.length;
  }

  /**
   * Gets detailed pool information
   * @returns {object} Detailed pool information
   */
  getDetailedInfo(): object {
    return {
      config: {
        max: this.pool.options.max,
        min: this.pool.options.min,
        idleTimeoutMillis: this.pool.options.idleTimeoutMillis,
        connectionTimeoutMillis: this.pool.options.connectionTimeoutMillis
      },
      stats: this.getStats(),
      queryTimePercentiles: this.getQueryTimePercentiles()
    };
  }

  /**
   * Calculates query time percentiles
   * @private
   * @returns {object} Query time percentiles
   */
  private getQueryTimePercentiles(): object {
    if (this.queryTimes.length === 0) {
      return { p50: 0, p90: 0, p95: 0, p99: 0 };
    }
    
    const sorted = [...this.queryTimes].sort((a, b) => a - b);
    const len = sorted.length;
    
    return {
      p50: sorted[Math.floor(len * 0.5)],
      p90: sorted[Math.floor(len * 0.9)],
      p95: sorted[Math.floor(len * 0.95)],
      p99: sorted[Math.floor(len * 0.99)]
    };
  }
}

/**
 * Singleton instance of connection pool manager
 */
export const dndConnectionPool = new DNDConnectionPoolManager();
