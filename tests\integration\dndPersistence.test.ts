import request from 'supertest';
import { app } from '@/server';
import { mockDb as db } from '@/database/mockConnection';
import nock from 'nock';
import { config } from '@/config';
import path from 'path';
import DNDPersistenceService from '@/services/dndPersistenceService';

describe('DND Persistence Integration', () => {
  let persistenceService: DNDPersistenceService;
  const baseUrl = '/api/dnd';

  beforeAll(async () => {
    await db.connect();
    persistenceService = new DNDPersistenceService();
  });

  afterAll(async () => {
    await db.disconnect();
  });

  beforeEach(async () => {
    // Clear any existing test data
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['test%']);
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['9876%']);
    
    // Clean up nock
    nock.cleanAll();
  });

  afterEach(() => {
    nock.cleanAll();
  });

  describe('CSV Upload with Persistence', () => {
    it('should save DND validation results to database after CSV processing', async () => {
      // Mock msgclub API response
      nock(config.dnd.apiBaseUrl)
        .get(config.dnd.apiEndpoint)
        .query(true)
        .reply(200, {
          "Wrong Number": [],
          "Non-NDNC": [9876543210, 9876543211],
          "NDNC": [9876543212],
          "response": "success"
        });

      // Create test CSV content
      const csvContent = `name,phone,email
Test User 1,+919876543210,<EMAIL>
Test User 2,+919876543211,<EMAIL>
Test User 3,+919876543212,<EMAIL>`;

      // Create temporary CSV file
      const fs = require('fs');
      const csvPath = path.join(__dirname, 'temp-test.csv');
      fs.writeFileSync(csvPath, csvContent);

      try {
        // Upload CSV and process DND validation
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary.totalRecords).toBe(3);
        expect(response.body.summary.processedRecords).toBe(3);

        // Verify results were saved to database
        const savedValidations = await persistenceService.getDNDValidations({
          search: '9876543'
        });

        expect(savedValidations.total).toBe(3);
        
        // Check specific validations
        const validation1 = await persistenceService.getDNDValidation('9876543210');
        expect(validation1).not.toBeNull();
        expect(validation1!.dndStatus).toBe('Non-DND');
        expect(validation1!.validationMetadata.name).toBe('Test User 1');
        expect(validation1!.validationMetadata.email).toBe('<EMAIL>');

        const validation3 = await persistenceService.getDNDValidation('9876543212');
        expect(validation3).not.toBeNull();
        expect(validation3!.dndStatus).toBe('DND');

      } finally {
        // Clean up temporary file
        if (fs.existsSync(csvPath)) {
          fs.unlinkSync(csvPath);
        }
      }
    });

    it('should handle API errors and save error status to database', async () => {
      // Mock msgclub API response with errors
      nock(config.dnd.apiBaseUrl)
        .get(config.dnd.apiEndpoint)
        .query(true)
        .reply(200, {
          "Wrong Number": [9876543220],
          "Non-NDNC": [],
          "NDNC": [],
          "response": "success"
        });

      const csvContent = `name,phone,email
Error User,+919876543220,<EMAIL>`;

      const fs = require('fs');
      const csvPath = path.join(__dirname, 'temp-error-test.csv');
      fs.writeFileSync(csvPath, csvContent);

      try {
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary.errorCount).toBe(1);

        // Verify error was saved to database
        const errorValidation = await persistenceService.getDNDValidation('9876543220');
        expect(errorValidation).not.toBeNull();
        expect(errorValidation!.dndStatus).toBe('Error');
        expect(errorValidation!.validationMetadata.errorMessage).toBe('Invalid phone number');

      } finally {
        if (fs.existsSync(csvPath)) {
          fs.unlinkSync(csvPath);
        }
      }
    });

    it('should continue processing even if database persistence fails', async () => {
      // Mock msgclub API response
      nock(config.dnd.apiBaseUrl)
        .get(config.dnd.apiEndpoint)
        .query(true)
        .reply(200, {
          "Wrong Number": [],
          "Non-NDNC": [9876543230],
          "NDNC": [],
          "response": "success"
        });

      // Temporarily break database connection to simulate persistence failure
      const originalQuery = db.query;
      db.query = jest.fn().mockRejectedValue(new Error('Database connection failed'));

      const csvContent = `name,phone,email
Test User,+919876543230,<EMAIL>`;

      const fs = require('fs');
      const csvPath = path.join(__dirname, 'temp-db-error-test.csv');
      fs.writeFileSync(csvPath, csvContent);

      try {
        // Should still return successful API response even if persistence fails
        const response = await request(app)
          .post(`${baseUrl}/upload`)
          .attach('csvFile', csvPath)
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.summary.nonDndCount).toBe(1);

      } finally {
        // Restore database connection
        db.query = originalQuery;
        
        if (fs.existsSync(csvPath)) {
          fs.unlinkSync(csvPath);
        }
      }
    });
  });

  describe('Single DND Check with Persistence', () => {
    it('should save single DND check result to database', async () => {
      const phoneNumber = '+919876543240';

      // Mock msgclub API response
      nock(config.dnd.apiBaseUrl)
        .get(config.dnd.apiEndpoint)
        .query(true)
        .reply(200, {
          "Wrong Number": [],
          "Non-NDNC": [],
          "NDNC": [9876543240],
          "response": "success"
        });

      // Make single DND check request
      const response = await request(app)
        .post(`${baseUrl}/validate-single`)
        .send({ phoneNumber })
        .expect(200);

      expect(response.body.dnd_status).toBe('DND');

      // Verify result was saved to database
      const savedValidation = await persistenceService.getDNDValidation(phoneNumber);
      expect(savedValidation).not.toBeNull();
      expect(savedValidation!.dndStatus).toBe('DND');
      expect(savedValidation!.validationMetadata.originalPhone).toBe(phoneNumber);
      expect(savedValidation!.validationMetadata.normalizedPhone).toBe('9876543240');
    });
  });

  describe('Database Query Integration', () => {
    beforeEach(async () => {
      // Create test data
      const testValidations = [
        { phone: 'test1111111111', dnd_status: 'DND' as const, name: 'User A' },
        { phone: 'test2222222222', dnd_status: 'Non-DND' as const, name: 'User B' },
        { phone: 'test3333333333', dnd_status: 'Error' as const, name: 'User C' }
      ];

      await persistenceService.saveBulkDNDValidations(testValidations);
    });

    it('should retrieve validation statistics', async () => {
      const stats = await persistenceService.getValidationStats();

      expect(stats.totalValidations).toBeGreaterThanOrEqual(3);
      expect(stats.dndCount).toBeGreaterThanOrEqual(1);
      expect(stats.nonDndCount).toBeGreaterThanOrEqual(1);
      expect(stats.errorCount).toBeGreaterThanOrEqual(1);
    });

    it('should handle pagination correctly', async () => {
      const page1 = await persistenceService.getDNDValidations({
        page: 1,
        limit: 2
      });

      expect(page1.validations.length).toBeLessThanOrEqual(2);
      expect(page1.total).toBeGreaterThanOrEqual(3);

      if (page1.total > 2) {
        const page2 = await persistenceService.getDNDValidations({
          page: 2,
          limit: 2
        });

        expect(page2.validations.length).toBeGreaterThan(0);
        
        // Ensure no overlap between pages
        const page1Phones = page1.validations.map(v => v.phoneNumber);
        const page2Phones = page2.validations.map(v => v.phoneNumber);
        const overlap = page1Phones.filter(phone => page2Phones.includes(phone));
        expect(overlap).toHaveLength(0);
      }
    });

    it('should filter validations by status', async () => {
      const dndValidations = await persistenceService.getDNDValidations({
        dndStatus: 'DND'
      });

      expect(dndValidations.validations.length).toBeGreaterThan(0);
      expect(dndValidations.validations.every(v => v.dndStatus === 'DND')).toBe(true);

      const nonDndValidations = await persistenceService.getDNDValidations({
        dndStatus: 'Non-DND'
      });

      expect(nonDndValidations.validations.length).toBeGreaterThan(0);
      expect(nonDndValidations.validations.every(v => v.dndStatus === 'Non-DND')).toBe(true);
    });

    it('should search validations by phone number', async () => {
      const searchResults = await persistenceService.getDNDValidations({
        search: '1111'
      });

      expect(searchResults.validations.length).toBeGreaterThan(0);
      expect(searchResults.validations.every(v => v.phoneNumber.includes('1111'))).toBe(true);
    });
  });

  describe('Upsert Behavior', () => {
    it('should update existing validation when phone number already exists', async () => {
      const phoneNumber = 'test4444444444';

      // Create initial validation
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'Non-DND',
        name: 'Initial User'
      });

      const initial = await persistenceService.getDNDValidation(phoneNumber);
      expect(initial!.dndStatus).toBe('Non-DND');
      expect(initial!.validationMetadata.name).toBe('Initial User');

      // Update with new validation
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'DND',
        name: 'Updated User'
      });

      const updated = await persistenceService.getDNDValidation(phoneNumber);
      expect(updated!.dndStatus).toBe('DND');
      expect(updated!.validationMetadata.name).toBe('Updated User');
      expect(updated!.createdAt).toEqual(initial!.createdAt); // Created date should remain same
      expect(updated!.updatedAt.getTime()).toBeGreaterThan(initial!.updatedAt.getTime()); // Updated date should change
    });
  });

  describe('Phone Number Normalization', () => {
    it('should normalize phone numbers consistently for storage and retrieval', async () => {
      const phoneVariations = [
        '+919876543250',
        '919876543250',
        '09876543250',
        '9876543250'
      ];

      // Save validation with first format
      await persistenceService.saveDNDValidation({
        phone: phoneVariations[0],
        dnd_status: 'DND'
      });

      // Should be able to retrieve with any format
      for (const phoneVariation of phoneVariations) {
        const retrieved = await persistenceService.getDNDValidation(phoneVariation);
        expect(retrieved).not.toBeNull();
        expect(retrieved!.phoneNumber).toBe('9876543250'); // Always normalized to 10 digits
        expect(retrieved!.dndStatus).toBe('DND');
      }
    });
  });
});
