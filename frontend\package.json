{"name": "tele-ai-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@headlessui/react": "^1.7.17", "@hookform/resolvers": "^3.3.2", "@types/papaparse": "^5.3.16", "@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@vitejs/plugin-react": "^4.1.1", "axios": "^1.6.2", "clsx": "^2.0.0", "date-fns": "^2.30.0", "lucide-react": "^0.294.0", "papaparse": "^5.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hook-form": "^7.48.2", "react-router-dom": "^6.20.1", "recharts": "^2.8.0", "socket.io-client": "^4.7.4", "vite": "^4.5.0", "zod": "^3.22.4", "zustand": "^4.4.7"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "autoprefixer": "^10.4.16", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "postcss": "^8.4.31", "tailwindcss": "^3.3.5", "typescript": "^5.2.2"}}