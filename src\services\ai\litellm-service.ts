import axios from 'axios';
import logger from '../../utils/logger';

export interface LiteLLMConfig {
  baseUrl: string;
  model: string;
  timeout: number;
  apiKey?: string;
}

export interface ChatMessage {
  role: 'system' | 'user' | 'assistant';
  content: string;
}

export interface LiteLLMResponse {
  choices: Array<{
    message: {
      role: string;
      content: string;
    };
    finish_reason: string;
  }>;
  usage?: {
    prompt_tokens: number;
    completion_tokens: number;
    total_tokens: number;
  };
}

export class LiteLLMService {
  private config: LiteLLMConfig;

  constructor(config: LiteLLMConfig) {
    this.config = config;
    logger.info(`🤖 LiteLLM service initialized with model: ${config.model}`);
  }

  async generateResponse(messages: ChatMessage[]): Promise<string | null> {
    try {
      logger.debug(`🤖 Generating response for ${messages.length} messages`);

      const response = await axios.post(
        `${this.config.baseUrl}/chat/completions`,
        {
          model: this.config.model,
          messages,
          max_tokens: 100, // Reduced for faster generation
          temperature: 0.5, // Lower temperature for faster, more focused responses
          stream: false,
          top_p: 0.9, // Nucleus sampling for faster generation
          frequency_penalty: 0.1, // Slight penalty to avoid repetition
          presence_penalty: 0.1
        },
        {
          headers: {
            'Content-Type': 'application/json',
            ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
          },
          timeout: this.config.timeout
        }
      );

      const result: LiteLLMResponse = response.data;
      const content = result.choices?.[0]?.message?.content;

      if (content) {
        logger.debug(`🤖 Generated response: "${content}"`);
        return content.trim();
      }

      logger.warn('❌ No content in LiteLLM response');
      return null;

    } catch (error) {
      logger.error('❌ LiteLLM API error:', error);
      
      // Return fallback response for phone calls
      return "I apologize, but I'm having trouble processing that right now. Could you please repeat your question?";
    }
  }

  async generateStreamingResponse(
    messages: ChatMessage[],
    onChunk: (chunk: string) => void
  ): Promise<void> {
    try {
      logger.debug(`🤖 Starting streaming response for ${messages.length} messages`);

      const response = await axios.post(
        `${this.config.baseUrl}/chat/completions`,
        {
          model: this.config.model,
          messages,
          max_tokens: 150,
          temperature: 0.7,
          stream: true
        },
        {
          headers: {
            'Content-Type': 'application/json',
            ...(this.config.apiKey && { 'Authorization': `Bearer ${this.config.apiKey}` })
          },
          timeout: this.config.timeout,
          responseType: 'stream'
        }
      );

      response.data.on('data', (chunk: Buffer) => {
        const lines = chunk.toString().split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            
            if (data === '[DONE]') {
              return;
            }

            try {
              const parsed = JSON.parse(data);
              const content = parsed.choices?.[0]?.delta?.content;
              
              if (content) {
                onChunk(content);
              }
            } catch (parseError) {
              // Ignore parsing errors for streaming chunks
            }
          }
        }
      });

      response.data.on('end', () => {
        logger.debug('🤖 Streaming response completed');
      });

    } catch (error) {
      logger.error('❌ LiteLLM streaming error:', error);
      onChunk("I apologize, but I'm having trouble processing that right now.");
    }
  }

  async healthCheck(): Promise<boolean> {
    try {
      const testMessages: ChatMessage[] = [
        { role: 'user', content: 'Hello' }
      ];

      const response = await this.generateResponse(testMessages);
      return response !== null;

    } catch (error) {
      logger.error('❌ LiteLLM health check failed:', error);
      return false;
    }
  }

  // Utility method for conversation summarization
  async summarizeConversation(messages: ChatMessage[]): Promise<string | null> {
    try {
      const summaryPrompt: ChatMessage[] = [
        {
          role: 'system',
          content: 'Summarize this conversation in 1-2 sentences, focusing on key points and outcomes.'
        },
        {
          role: 'user',
          content: `Conversation to summarize:\n${messages.map(m => `${m.role}: ${m.content}`).join('\n')}`
        }
      ];

      return await this.generateResponse(summaryPrompt);

    } catch (error) {
      logger.error('❌ Error summarizing conversation:', error);
      return null;
    }
  }

  // Utility method for intent classification
  async classifyIntent(userMessage: string, possibleIntents: string[]): Promise<string | null> {
    try {
      const intentPrompt: ChatMessage[] = [
        {
          role: 'system',
          content: `Classify the user's intent from this list: ${possibleIntents.join(', ')}. Respond with only the intent name.`
        },
        {
          role: 'user',
          content: userMessage
        }
      ];

      const response = await this.generateResponse(intentPrompt);
      
      if (response && possibleIntents.includes(response.toLowerCase())) {
        return response.toLowerCase();
      }

      return null;

    } catch (error) {
      logger.error('❌ Error classifying intent:', error);
      return null;
    }
  }

  // Utility method for data extraction
  async extractStructuredData(
    userMessage: string,
    schema: Record<string, string>
  ): Promise<Record<string, any> | null> {
    try {
      const extractionPrompt: ChatMessage[] = [
        {
          role: 'system',
          content: `Extract structured data from the user's message according to this schema: ${JSON.stringify(schema)}. Return only valid JSON with extracted values, or {} if nothing found.`
        },
        {
          role: 'user',
          content: userMessage
        }
      ];

      const response = await this.generateResponse(extractionPrompt);
      
      if (response) {
        try {
          return JSON.parse(response);
        } catch (parseError) {
          logger.warn('❌ Could not parse extracted data as JSON');
        }
      }

      return null;

    } catch (error) {
      logger.error('❌ Error extracting structured data:', error);
      return null;
    }
  }
}
