import axios from 'axios';
import { DNDValidationResponse, DNDConfig } from '../types';

// Get API base URL from environment or use relative path for production
const getApiBaseUrl = () => {
  // In development, use localhost
  if (import.meta.env.DEV) {
    return 'http://localhost:3000/api/v1';
  }
  // In production, use relative path (same domain)
  return '/api/v1';
};

// Create axios instance specifically for DND operations with longer timeout
const dndApi = axios.create({
  baseURL: getApiBaseUrl(),
  timeout: 300000, // 5 minutes for large file processing
  headers: {
    'Content-Type': 'multipart/form-data',
  },
});

// Add request interceptor for authentication
dndApi.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
dndApi.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('DND API Error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// DND API endpoints
export const dndApiClient = {
  // Upload CSV file for DND validation
  uploadCSV: async (
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<DNDValidationResponse> => {
    const formData = new FormData();
    formData.append('csvFile', file);

    const response = await dndApi.post('/dnd/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  },

  // Get service health status
  getHealth: async (): Promise<{ status: string; timestamp: string }> => {
    const response = await dndApi.get('/dnd/health');
    return response.data;
  },

  // Get service configuration
  getConfig: async (): Promise<DNDConfig> => {
    const response = await dndApi.get('/dnd/config');
    return response.data;
  },

  // Validate single phone number (for testing)
  validateSingle: async (phone: string): Promise<{
    phone: string;
    dnd_status: 'DND' | 'Non-DND' | 'Error';
    registry?: string;
    error_message?: string;
  }> => {
    const response = await dndApi.post('/dnd/validate-single', { phone });
    return response.data;
  },
};

export default dndApiClient;
