/**
 * @fileoverview DND Audit Logger for Compliance and Monitoring
 * @description Comprehensive audit logging for DND persistence operations with GDPR compliance
 * <AUTHOR> System
 * @version 1.0.0
 */

import { IDNDAuditLogger, DNDValidation, DNDAuditLogEntry } from '../types';
import { DND_AUDIT_EVENTS } from '../config/dndPersistence';
import { db } from '../database/connection';
import logger from './logger';
import { v4 as uuidv4 } from 'uuid';

/**
 * Audit logger for DND persistence operations
 * @implements {IDNDAuditLogger}
 */
export class DNDAuditLogger implements IDNDAuditLogger {
  private readonly tableName = 'dnd_audit_logs';
  private readonly enabled: boolean;

  /**
   * Creates a new DNDAuditLogger instance
   * @param {boolean} enabled - Whether audit logging is enabled
   */
  constructor(enabled: boolean = true) {
    this.enabled = enabled;
  }

  /**
   * Logs a generic audit event
   * @param {string} event - Event type
   * @param {Record<string, any>} data - Event data
   * @param {string} userId - User ID who performed the action
   * @returns {Promise<void>}
   */
  async logEvent(event: string, data: Record<string, any>, userId?: string): Promise<void> {
    if (!this.enabled) {
      return;
    }

    try {
      const auditEntry: DNDAuditLogEntry = {
        id: uuidv4(),
        event,
        phoneNumber: data.phoneNumber,
        userId,
        data: this.sanitizeAuditData(data),
        timestamp: new Date(),
        source: 'dnd-persistence-service'
      };

      await this.persistAuditLog(auditEntry);
      
      // Also log to application logger for immediate visibility
      logger.info('DND Audit Event', {
        event,
        phoneNumber: data.phoneNumber,
        userId,
        timestamp: auditEntry.timestamp.toISOString()
      });
    } catch (error) {
      // Don't throw errors from audit logging to avoid breaking main operations
      logger.error('Failed to log audit event:', error);
    }
  }

  /**
   * Logs validation creation event
   * @param {DNDValidation} validation - Created validation
   * @param {string} userId - User ID who created the validation
   * @returns {Promise<void>}
   */
  async logValidationCreated(validation: DNDValidation, userId?: string): Promise<void> {
    await this.logEvent(DND_AUDIT_EVENTS.VALIDATION_CREATED, {
      phoneNumber: validation.phoneNumber,
      dndStatus: validation.dndStatus,
      validatedAt: validation.validatedAt.toISOString(),
      hasMetadata: Object.keys(validation.validationMetadata || {}).length > 0
    }, userId);
  }

  /**
   * Logs validation update event
   * @param {DNDValidation} validation - Updated validation
   * @param {string} userId - User ID who updated the validation
   * @returns {Promise<void>}
   */
  async logValidationUpdated(validation: DNDValidation, userId?: string): Promise<void> {
    await this.logEvent(DND_AUDIT_EVENTS.VALIDATION_UPDATED, {
      phoneNumber: validation.phoneNumber,
      dndStatus: validation.dndStatus,
      validatedAt: validation.validatedAt.toISOString(),
      updatedAt: validation.updatedAt.toISOString()
    }, userId);
  }

  /**
   * Logs validation deletion event
   * @param {string} phoneNumber - Phone number of deleted validation
   * @param {string} userId - User ID who deleted the validation
   * @returns {Promise<void>}
   */
  async logValidationDeleted(phoneNumber: string, userId?: string): Promise<void> {
    await this.logEvent(DND_AUDIT_EVENTS.VALIDATION_DELETED, {
      phoneNumber,
      deletedAt: new Date().toISOString()
    }, userId);
  }

  /**
   * Logs bulk operation event
   * @param {string} operation - Type of bulk operation
   * @param {number} count - Number of records affected
   * @param {string} userId - User ID who performed the operation
   * @returns {Promise<void>}
   */
  async logBulkOperation(operation: string, count: number, userId?: string): Promise<void> {
    await this.logEvent(DND_AUDIT_EVENTS.BULK_VALIDATION_CREATED, {
      operation,
      recordCount: count,
      executedAt: new Date().toISOString()
    }, userId);
  }

  /**
   * Logs cleanup operation event
   * @param {number} deletedCount - Number of records deleted
   * @param {number} retentionDays - Retention period in days
   * @param {string} userId - User ID who performed the cleanup
   * @returns {Promise<void>}
   */
  async logCleanupOperation(deletedCount: number, retentionDays: number, userId?: string): Promise<void> {
    await this.logEvent(DND_AUDIT_EVENTS.CLEANUP_EXECUTED, {
      deletedCount,
      retentionDays,
      executedAt: new Date().toISOString()
    }, userId);
  }

  /**
   * Logs health check event
   * @param {Record<string, any>} healthData - Health check results
   * @returns {Promise<void>}
   */
  async logHealthCheck(healthData: Record<string, any>): Promise<void> {
    await this.logEvent(DND_AUDIT_EVENTS.HEALTH_CHECK_PERFORMED, {
      ...healthData,
      executedAt: new Date().toISOString()
    });
  }

  /**
   * Retrieves audit logs for a specific phone number
   * @param {string} phoneNumber - Phone number to get audit logs for
   * @param {number} limit - Maximum number of logs to retrieve
   * @returns {Promise<DNDAuditLogEntry[]>} Array of audit log entries
   */
  async getAuditLogsForPhone(phoneNumber: string, limit: number = 100): Promise<DNDAuditLogEntry[]> {
    if (!this.enabled) {
      return [];
    }

    try {
      const result = await db.query(`
        SELECT id, event, phone_number, user_id, data, timestamp, source
        FROM ${this.tableName}
        WHERE phone_number = $1
        ORDER BY timestamp DESC
        LIMIT $2
      `, [phoneNumber, limit]);

      return result.rows.map(this.mapRowToAuditEntry);
    } catch (error) {
      logger.error('Failed to retrieve audit logs:', error);
      return [];
    }
  }

  /**
   * Retrieves audit logs for a specific user
   * @param {string} userId - User ID to get audit logs for
   * @param {number} limit - Maximum number of logs to retrieve
   * @returns {Promise<DNDAuditLogEntry[]>} Array of audit log entries
   */
  async getAuditLogsForUser(userId: string, limit: number = 100): Promise<DNDAuditLogEntry[]> {
    if (!this.enabled) {
      return [];
    }

    try {
      const result = await db.query(`
        SELECT id, event, phone_number, user_id, data, timestamp, source
        FROM ${this.tableName}
        WHERE user_id = $1
        ORDER BY timestamp DESC
        LIMIT $2
      `, [userId, limit]);

      return result.rows.map(this.mapRowToAuditEntry);
    } catch (error) {
      logger.error('Failed to retrieve user audit logs:', error);
      return [];
    }
  }

  /**
   * Deletes audit logs older than specified days (for GDPR compliance)
   * @param {number} retentionDays - Number of days to retain logs
   * @returns {Promise<number>} Number of deleted log entries
   */
  async cleanupAuditLogs(retentionDays: number): Promise<number> {
    if (!this.enabled) {
      return 0;
    }

    try {
      const result = await db.query(`
        DELETE FROM ${this.tableName}
        WHERE timestamp < NOW() - INTERVAL '${retentionDays} days'
      `);

      const deletedCount = result.rowCount || 0;
      
      if (deletedCount > 0) {
        logger.info(`Cleaned up ${deletedCount} audit log entries older than ${retentionDays} days`);
      }

      return deletedCount;
    } catch (error) {
      logger.error('Failed to cleanup audit logs:', error);
      return 0;
    }
  }

  /**
   * Persists audit log entry to database
   * @private
   * @param {DNDAuditLogEntry} entry - Audit log entry to persist
   * @returns {Promise<void>}
   */
  private async persistAuditLog(entry: DNDAuditLogEntry): Promise<void> {
    try {
      await db.query(`
        INSERT INTO ${this.tableName} (id, event, phone_number, user_id, data, timestamp, source)
        VALUES ($1, $2, $3, $4, $5, $6, $7)
      `, [
        entry.id,
        entry.event,
        entry.phoneNumber,
        entry.userId,
        JSON.stringify(entry.data),
        entry.timestamp,
        entry.source
      ]);
    } catch (error) {
      // If audit table doesn't exist, create it
      if (error.message?.includes('does not exist')) {
        await this.createAuditTable();
        // Retry the insert
        await db.query(`
          INSERT INTO ${this.tableName} (id, event, phone_number, user_id, data, timestamp, source)
          VALUES ($1, $2, $3, $4, $5, $6, $7)
        `, [
          entry.id,
          entry.event,
          entry.phoneNumber,
          entry.userId,
          JSON.stringify(entry.data),
          entry.timestamp,
          entry.source
        ]);
      } else {
        throw error;
      }
    }
  }

  /**
   * Creates audit log table if it doesn't exist
   * @private
   * @returns {Promise<void>}
   */
  private async createAuditTable(): Promise<void> {
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS ${this.tableName} (
        id VARCHAR(36) PRIMARY KEY,
        event VARCHAR(100) NOT NULL,
        phone_number VARCHAR(20),
        user_id VARCHAR(100),
        data JSONB NOT NULL DEFAULT '{}',
        timestamp TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
        source VARCHAR(100) NOT NULL DEFAULT 'dnd-persistence-service'
      );

      CREATE INDEX IF NOT EXISTS idx_dnd_audit_logs_phone_number ON ${this.tableName}(phone_number);
      CREATE INDEX IF NOT EXISTS idx_dnd_audit_logs_user_id ON ${this.tableName}(user_id);
      CREATE INDEX IF NOT EXISTS idx_dnd_audit_logs_timestamp ON ${this.tableName}(timestamp);
      CREATE INDEX IF NOT EXISTS idx_dnd_audit_logs_event ON ${this.tableName}(event);
    `;

    await db.query(createTableSQL);
    logger.info('Created DND audit logs table');
  }

  /**
   * Sanitizes audit data to remove sensitive information
   * @private
   * @param {Record<string, any>} data - Data to sanitize
   * @returns {Record<string, any>} Sanitized data
   */
  private sanitizeAuditData(data: Record<string, any>): Record<string, any> {
    const sanitized = { ...data };
    
    // Remove or mask sensitive fields
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    
    for (const field of sensitiveFields) {
      if (sanitized[field]) {
        sanitized[field] = '[REDACTED]';
      }
    }

    // Limit data size
    const dataString = JSON.stringify(sanitized);
    if (dataString.length > 10000) {
      return { 
        ...sanitized, 
        _truncated: true, 
        _originalSize: dataString.length 
      };
    }

    return sanitized;
  }

  /**
   * Maps database row to audit entry object
   * @private
   * @param {any} row - Database row
   * @returns {DNDAuditLogEntry} Audit log entry
   */
  private mapRowToAuditEntry(row: any): DNDAuditLogEntry {
    return {
      id: row.id,
      event: row.event,
      phoneNumber: row.phone_number,
      userId: row.user_id,
      data: typeof row.data === 'string' ? JSON.parse(row.data) : row.data,
      timestamp: new Date(row.timestamp),
      source: row.source
    };
  }
}

/**
 * Singleton instance of DNDAuditLogger
 */
export const dndAuditLogger = new DNDAuditLogger(
  process.env.NODE_ENV === 'production' || process.env.DND_AUDIT_LOGGING === 'true'
);
