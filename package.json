{"name": "ai-telecalling-mvp", "version": "1.0.0", "description": "AI-powered telecalling platform MVP for Indian SMB market", "main": "dist/index.js", "scripts": {"dev": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:backend": "tsx watch src/index.ts", "dev:frontend": "cd frontend && npm run dev", "dev:ngrok": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\" \"npm run ngrok\"", "build": "npm run build:backend && npm run build:frontend", "build:backend": "tsc", "build:frontend": "cd frontend && npm run build", "start": "node dist/index.js", "server": "tsx src/server.ts", "demo": "tsx demo/calling-demo.ts", "ngrok": "node scripts/start-ngrok.js", "ngrok:simple": "npx ngrok http 3000", "ngrok:setup": "node scripts/setup-ngrok.js", "test": "jest", "test:watch": "jest --watch", "test:integration": "jest --config jest.integration.config.js", "test:all": "npm run test && npm run test:integration", "db:migrate": "tsx src/database/migrate.ts", "db:seed": "tsx src/database/seed.ts", "lint": "eslint src/**/*.ts && cd frontend && npm run lint", "lint:fix": "eslint src/**/*.ts --fix && cd frontend && npm run lint:fix", "setup": "npm install && cd frontend && npm install"}, "keywords": ["ai", "telecalling", "voice", "saas", "mvp"], "author": "AI Telecalling Team", "license": "MIT", "dependencies": {"@types/twilio": "^3.19.3", "@types/ws": "^8.18.1", "axios": "^1.6.2", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.2.0", "dotenv": "^16.6.1", "express": "^4.18.2", "form-data": "^4.0.3", "helmet": "^7.1.0", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "multer": "^1.4.5-lts.1", "nock": "^14.0.5", "p-limit": "^3.1.0", "pg": "^8.11.3", "twilio": "^4.23.0", "winston": "^3.11.0", "ws": "^8.18.3"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jest": "^29.5.8", "@types/jsonwebtoken": "^9.0.5", "@types/multer": "^1.4.11", "@types/node": "^20.10.0", "@types/pg": "^8.10.9", "@types/supertest": "^2.0.16", "@typescript-eslint/eslint-plugin": "^6.12.0", "@typescript-eslint/parser": "^6.12.0", "concurrently": "^8.2.2", "eslint": "^8.54.0", "jest": "^29.7.0", "ngrok": "^5.0.0-beta.2", "nodemon": "^3.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "tsx": "^4.6.0", "typescript": "^5.3.2"}, "engines": {"node": ">=18.0.0"}}