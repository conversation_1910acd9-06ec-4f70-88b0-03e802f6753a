import React, { useState } from 'react';
import { ChevronUp, ChevronDown, Mail, Phone, User, AlertCircle } from 'lucide-react';
import { Button } from '../ui/Button';
import { DndStatusBadge } from './DndStatusBadge';
import { DNDRecord, DNDUIState } from '../../types';

interface DndResultsTableProps {
  data: DNDRecord[];
  uiState: DNDUIState;
  onUpdateSort: (sortBy: keyof DNDRecord, sortOrder?: 'asc' | 'desc') => void;
  selectedRows?: Set<number>;
  onRowSelect?: (index: number, selected: boolean) => void;
  onSelectAll?: (selected: boolean) => void;
}

export const DndResultsTable: React.FC<DndResultsTableProps> = ({
  data,
  uiState,
  onUpdateSort,
  selectedRows = new Set(),
  onRowSelect,
  onSelectAll,
}) => {
  const [hoveredRow, setHoveredRow] = useState<number | null>(null);

  const handleSort = (column: keyof DNDRecord) => {
    onUpdateSort(column);
  };

  const getSortIcon = (column: keyof DNDRecord) => {
    if (uiState.sortBy !== column) {
      return <ChevronUp className="h-4 w-4 text-gray-400" />;
    }
    return uiState.sortOrder === 'asc' ? (
      <ChevronUp className="h-4 w-4 text-primary-600" />
    ) : (
      <ChevronDown className="h-4 w-4 text-primary-600" />
    );
  };

  const handleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    onSelectAll?.(e.target.checked);
  };

  const handleRowSelect = (index: number, e: React.ChangeEvent<HTMLInputElement>) => {
    onRowSelect?.(index, e.target.checked);
  };

  const getRowClassName = (record: DNDRecord, index: number) => {
    let baseClass = 'transition-colors duration-150';
    
    if (selectedRows.has(index)) {
      baseClass += ' bg-primary-50 border-primary-200';
    } else if (hoveredRow === index) {
      baseClass += ' bg-gray-50';
    } else {
      baseClass += ' bg-white';
    }

    // Add status-based styling
    switch (record.dnd_status) {
      case 'DND':
        baseClass += ' border-l-4 border-l-red-400';
        break;
      case 'Non-DND':
        baseClass += ' border-l-4 border-l-green-400';
        break;
      case 'Error':
        baseClass += ' border-l-4 border-l-yellow-400';
        break;
    }

    return baseClass;
  };

  if (data.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto h-12 w-12 text-gray-400">
          <svg fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </div>
        <h3 className="mt-2 text-sm font-medium text-gray-900">No results</h3>
        <p className="mt-1 text-sm text-gray-500">No DND validation results to display.</p>
      </div>
    );
  }

  return (
    <div className="overflow-hidden shadow ring-1 ring-black ring-opacity-5 md:rounded-lg">
      <table className="min-w-full divide-y divide-gray-300">
        <thead className="bg-gray-50">
          <tr>
            {onRowSelect && (
              <th scope="col" className="relative w-12 px-6 sm:w-16 sm:px-8">
                <input
                  type="checkbox"
                  className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                  checked={selectedRows.size === data.length && data.length > 0}
                  onChange={handleSelectAll}
                />
              </th>
            )}
            
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <button
                onClick={() => handleSort('name')}
                className="flex items-center space-x-1 text-left font-medium text-xs uppercase tracking-wider text-gray-500 hover:text-gray-700"
              >
                <User className="h-4 w-4" />
                <span>Name</span>
                {getSortIcon('name')}
              </button>
            </th>
            
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <button
                onClick={() => handleSort('phone')}
                className="flex items-center space-x-1 text-left font-medium text-xs uppercase tracking-wider text-gray-500 hover:text-gray-700"
              >
                <Phone className="h-4 w-4" />
                <span>Phone</span>
                {getSortIcon('phone')}
              </button>
            </th>

            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <button
                onClick={() => handleSort('email')}
                className="flex items-center space-x-1 text-left font-medium text-xs uppercase tracking-wider text-gray-500 hover:text-gray-700"
              >
                <Mail className="h-4 w-4" />
                <span>Email</span>
                {getSortIcon('email')}
              </button>
            </th>

            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              <button
                onClick={() => handleSort('dnd_status')}
                className="flex items-center space-x-1 text-left font-medium text-xs uppercase tracking-wider text-gray-500 hover:text-gray-700"
              >
                <span>DND Status</span>
                {getSortIcon('dnd_status')}
              </button>
            </th>
            
            <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Error Details
            </th>
          </tr>
        </thead>
        
        <tbody className="bg-white divide-y divide-gray-200">
          {data.map((record, index) => (
            <tr
              key={index}
              className={getRowClassName(record, index)}
              onMouseEnter={() => setHoveredRow(index)}
              onMouseLeave={() => setHoveredRow(null)}
            >
              {onRowSelect && (
                <td className="relative w-12 px-6 sm:w-16 sm:px-8">
                  <input
                    type="checkbox"
                    className="absolute left-4 top-1/2 -mt-2 h-4 w-4 rounded border-gray-300 text-primary-600 focus:ring-primary-500"
                    checked={selectedRows.has(index)}
                    onChange={(e) => handleRowSelect(index, e)}
                  />
                </td>
              )}
              
              <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                {record.name}
              </td>
              
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex items-center">
                  <Phone className="h-4 w-4 text-gray-400 mr-2" />
                  {record.phone}
                </div>
              </td>
              
              <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div className="flex items-center">
                  <Mail className="h-4 w-4 text-gray-400 mr-2" />
                  {record.email}
                </div>
              </td>
              
              <td className="px-6 py-4 whitespace-nowrap">
                <DndStatusBadge status={record.dnd_status} />
              </td>
              
              <td className="px-6 py-4 text-sm text-gray-500">
                {record.error_message ? (
                  <div className="flex items-start space-x-2">
                    <AlertCircle className="h-4 w-4 text-yellow-500 mt-0.5 flex-shrink-0" />
                    <span className="max-w-xs truncate" title={record.error_message}>
                      {record.error_message}
                    </span>
                  </div>
                ) : (
                  <span className="text-gray-400">—</span>
                )}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};
