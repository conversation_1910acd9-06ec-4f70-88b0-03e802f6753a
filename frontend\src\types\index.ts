// Shared types between frontend and backend
export interface User {
  id: number;
  email: string;
  name: string;
  role: 'admin' | 'user';
  createdAt: string;
  updatedAt: string;
}

// Authentication types
export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  logout: () => void;
}

export interface Lead {
  id: number;
  name: string;
  phone: string;
  email?: string;
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'rejected';
  notes?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Call {
  id: number;
  leadId: number;
  agentId?: number;
  twilioCallSid?: string;
  status: 'initiated' | 'ringing' | 'in-progress' | 'completed' | 'failed' | 'no-answer';
  callType: 'manual' | 'scheduled' | 'auto';
  duration?: number;
  recordingUrl?: string;
  transcript?: string;
  aiResponse?: string;
  startedAt?: string;
  endedAt?: string;
  createdAt: string;
  updatedAt: string;
}

export interface Agent {
  id: number;
  name: string;
  email: string;
  phoneNumber: string;
  skills: string[];
  status: 'active' | 'inactive' | 'busy';
  assignedLeads: number;
  totalCalls: number;
  successRate: number;
  createdAt: string;
  updatedAt: string;
}

// API Request types
export interface CreateLeadRequest {
  name: string;
  phone: string;
  email?: string;
  notes?: string;
  assignedAgentId?: number;
}

export interface UpdateLeadRequest {
  name?: string;
  phone?: string;
  email?: string;
  status?: Lead['status'];
  notes?: string;
  assignedAgentId?: number;
}

export interface CreateAgentRequest {
  name: string;
  email: string;
  phoneNumber: string;
  skills: string[];
}

export interface UpdateAgentRequest {
  name?: string;
  email?: string;
  phoneNumber?: string;
  skills?: string[];
  status?: Agent['status'];
}

export interface InitiateCallRequest {
  leadId: number;
  message?: string;
}

export interface AuthRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: Omit<User, 'password'>;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
}

export interface ApiError {
  error: {
    message: string;
    code?: string;
    statusCode: number;
  };
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// UI specific types
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, item: T) => React.ReactNode;
}

export interface FilterOption {
  label: string;
  value: string;
}

export interface DashboardStats {
  totalLeads: number;
  totalCalls: number;
  successfulCalls: number;
  conversionRate: number;
}

// Form types
export interface LeadFormData {
  name: string;
  phone: string;
  email: string;
  notes: string;
}

export interface CallFormData {
  leadId: number;
  message: string;
}

// Navigation types
export interface NavItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  current?: boolean;
}

// DND Validation types
export interface DNDRecord {
  name: string;
  phone: string;
  email: string;
  dnd_status: 'DND' | 'Non-DND' | 'Error';
  error_message?: string;
}

export interface DNDValidationError {
  row: number;
  reason: string;
}

export interface DNDValidationSummary {
  totalRecords: number;
  processedRecords: number;
  skippedRecords: number;
  dndCount: number;
  nonDndCount: number;
  errorCount: number;
}

export interface DNDValidationMetadata {
  processingTimeMs: number;
  fileName: string;
  fileSize: number;
  timestamp: string;
}

export interface DNDValidationResponse {
  success: boolean;
  summary: DNDValidationSummary;
  results: DNDRecord[];
  errors: DNDValidationError[];
  metadata: DNDValidationMetadata;
}

export interface DNDUploadState {
  file: File | null;
  isUploading: boolean;
  progress: number;
  error: string | null;
}

export interface DNDResultsState {
  data: DNDRecord[];
  summary: DNDValidationSummary | null;
  errors: DNDValidationError[];
  metadata: DNDValidationMetadata | null;
  isLoading: boolean;
}

export interface DNDUIState {
  currentPage: number;
  pageSize: number;
  sortBy: keyof DNDRecord;
  sortOrder: 'asc' | 'desc';
  filters: {
    dndStatus: 'all' | 'DND' | 'Non-DND' | 'Error';
    searchTerm: string;
    showErrorsOnly: boolean;
  };
}

export interface DNDState {
  upload: DNDUploadState;
  results: DNDResultsState;
  ui: DNDUIState;
}

export interface DNDConfig {
  maxFileSizeMB: number;
  maxRecordsLimit: number;
  supportedFormats: string[];
  requiredColumns: string[];
}
