import { DNDValidationModel } from '../database/models/DNDValidation';
import { DNDValidation, CreateDNDValidationRequest, DNDValidationStats } from '../types';
import { DNDCheckResult, BulkDNDResponse } from './dndService';
import { normalizePhoneNumber } from '../utils/phoneNormalizer';
import logger from '../utils/logger';

export interface PersistenceOptions {
  skipRecentValidations?: boolean;
  recentValidationHours?: number;
  includeMetadata?: boolean;
}

export class DNDPersistenceService {
  /**
   * Save a single DND validation result to the database
   */
  async saveDNDValidation(
    result: DNDCheckResult,
    options: PersistenceOptions = {}
  ): Promise<DNDValidation> {
    const { skipRecentValidations = false, recentValidationHours = 24, includeMetadata = true } = options;

    // Normalize the phone number for consistent storage
    const normalizedResult = normalizePhoneNumber(result.phone);
    const phoneNumber = normalizedResult.isValid ? normalizedResult.normalized : result.phone;

    try {
      // Check if we should skip recent validations
      if (skipRecentValidations) {
        const isRecent = await DNDValidationModel.isRecentlyValidated(phoneNumber, recentValidationHours);
        if (isRecent) {
          logger.debug(`Skipping recent validation for phone: ${phoneNumber}`);
          const existing = await DNDValidationModel.findByPhone(phoneNumber);
          if (existing) {
            return existing;
          }
        }
      }

      // Prepare validation metadata
      const validationMetadata: Record<string, any> = {};
      
      if (includeMetadata) {
        validationMetadata.originalPhone = result.phone;
        validationMetadata.normalizedPhone = phoneNumber;
        validationMetadata.isValidFormat = normalizedResult.isValid;
        
        if (result.name) {
          validationMetadata.name = result.name;
        }
        
        if (result.email) {
          validationMetadata.email = result.email;
        }
        
        if (result.error_message) {
          validationMetadata.errorMessage = result.error_message;
        }
        
        if (!normalizedResult.isValid && normalizedResult.error) {
          validationMetadata.normalizationError = normalizedResult.error;
        }
      }

      const validationData: CreateDNDValidationRequest = {
        phoneNumber,
        dndStatus: result.dnd_status,
        validatedAt: new Date(),
        validationMetadata
      };

      const savedValidation = await DNDValidationModel.upsert(validationData);
      
      logger.info(`Saved DND validation for phone: ${phoneNumber}, status: ${result.dnd_status}`);
      return savedValidation;

    } catch (error) {
      logger.error(`Error saving DND validation for phone ${phoneNumber}:`, error);
      throw error;
    }
  }

  /**
   * Save multiple DND validation results to the database
   */
  async saveBulkDNDValidations(
    results: DNDCheckResult[],
    options: PersistenceOptions = {}
  ): Promise<DNDValidation[]> {
    const { skipRecentValidations = false, recentValidationHours = 24, includeMetadata = true } = options;

    if (results.length === 0) {
      return [];
    }

    try {
      logger.info(`Saving ${results.length} DND validation results to database`);

      // Prepare validation data for bulk insert
      const validationDataList: CreateDNDValidationRequest[] = [];
      const skippedPhones: string[] = [];

      for (const result of results) {
        // Normalize the phone number for consistent storage
        const normalizedResult = normalizePhoneNumber(result.phone);
        const phoneNumber = normalizedResult.isValid ? normalizedResult.normalized : result.phone;

        // Check if we should skip recent validations
        if (skipRecentValidations) {
          const isRecent = await DNDValidationModel.isRecentlyValidated(phoneNumber, recentValidationHours);
          if (isRecent) {
            skippedPhones.push(phoneNumber);
            continue;
          }
        }

        // Prepare validation metadata
        const validationMetadata: Record<string, any> = {};
        
        if (includeMetadata) {
          validationMetadata.originalPhone = result.phone;
          validationMetadata.normalizedPhone = phoneNumber;
          validationMetadata.isValidFormat = normalizedResult.isValid;
          
          if (result.name) {
            validationMetadata.name = result.name;
          }
          
          if (result.email) {
            validationMetadata.email = result.email;
          }
          
          if (result.error_message) {
            validationMetadata.errorMessage = result.error_message;
          }
          
          if (!normalizedResult.isValid && normalizedResult.error) {
            validationMetadata.normalizationError = normalizedResult.error;
          }
        }

        validationDataList.push({
          phoneNumber,
          dndStatus: result.dnd_status,
          validatedAt: new Date(),
          validationMetadata
        });
      }

      if (skippedPhones.length > 0) {
        logger.info(`Skipped ${skippedPhones.length} recent validations`);
      }

      if (validationDataList.length === 0) {
        logger.info('No new validations to save (all were recent)');
        return [];
      }

      // Perform bulk upsert
      const savedValidations = await DNDValidationModel.bulkUpsert(validationDataList);
      
      logger.info(`Successfully saved ${savedValidations.length} DND validations to database`);
      return savedValidations;

    } catch (error) {
      logger.error('Error saving bulk DND validations:', error);
      throw error;
    }
  }

  /**
   * Save DND validation results from a bulk response
   */
  async saveBulkDNDResponse(
    bulkResponse: BulkDNDResponse,
    options: PersistenceOptions = {}
  ): Promise<DNDValidation[]> {
    return this.saveBulkDNDValidations(bulkResponse.results, options);
  }

  /**
   * Get DND validation by phone number
   */
  async getDNDValidation(phoneNumber: string): Promise<DNDValidation | null> {
    // Normalize the phone number for lookup
    const normalizedResult = normalizePhoneNumber(phoneNumber);
    const lookupPhone = normalizedResult.isValid ? normalizedResult.normalized : phoneNumber;

    try {
      return await DNDValidationModel.findByPhone(lookupPhone);
    } catch (error) {
      logger.error(`Error getting DND validation for phone ${lookupPhone}:`, error);
      throw error;
    }
  }

  /**
   * Check if a phone number has been validated recently
   */
  async isRecentlyValidated(phoneNumber: string, withinHours: number = 24): Promise<boolean> {
    // Normalize the phone number for lookup
    const normalizedResult = normalizePhoneNumber(phoneNumber);
    const lookupPhone = normalizedResult.isValid ? normalizedResult.normalized : phoneNumber;

    try {
      return await DNDValidationModel.isRecentlyValidated(lookupPhone, withinHours);
    } catch (error) {
      logger.error(`Error checking recent validation for phone ${lookupPhone}:`, error);
      throw error;
    }
  }

  /**
   * Get DND validation statistics
   */
  async getValidationStats(): Promise<DNDValidationStats> {
    try {
      return await DNDValidationModel.getStats();
    } catch (error) {
      logger.error('Error getting DND validation stats:', error);
      throw error;
    }
  }

  /**
   * Get paginated DND validations with filtering
   */
  async getDNDValidations(options: {
    page?: number;
    limit?: number;
    dndStatus?: 'DND' | 'Non-DND' | 'Error';
    search?: string;
  } = {}): Promise<{ validations: DNDValidation[]; total: number }> {
    try {
      return await DNDValidationModel.findAll(options);
    } catch (error) {
      logger.error('Error getting DND validations:', error);
      throw error;
    }
  }

  /**
   * Delete DND validation by phone number
   */
  async deleteDNDValidation(phoneNumber: string): Promise<void> {
    // Normalize the phone number for lookup
    const normalizedResult = normalizePhoneNumber(phoneNumber);
    const lookupPhone = normalizedResult.isValid ? normalizedResult.normalized : phoneNumber;

    try {
      await DNDValidationModel.delete(lookupPhone);
      logger.info(`Deleted DND validation for phone: ${lookupPhone}`);
    } catch (error) {
      logger.error(`Error deleting DND validation for phone ${lookupPhone}:`, error);
      throw error;
    }
  }

  /**
   * Clean up old DND validations (older than specified days)
   */
  async cleanupOldValidations(olderThanDays: number = 90): Promise<number> {
    try {
      const result = await DNDValidationModel.findAll({
        page: 1,
        limit: 1000 // Process in batches
      });

      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - olderThanDays);

      let deletedCount = 0;
      for (const validation of result.validations) {
        if (validation.validatedAt < cutoffDate) {
          await DNDValidationModel.delete(validation.phoneNumber);
          deletedCount++;
        }
      }

      logger.info(`Cleaned up ${deletedCount} old DND validations (older than ${olderThanDays} days)`);
      return deletedCount;
    } catch (error) {
      logger.error('Error cleaning up old DND validations:', error);
      throw error;
    }
  }
}

export default DNDPersistenceService;
