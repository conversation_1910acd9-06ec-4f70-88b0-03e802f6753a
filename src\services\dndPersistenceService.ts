/**
 * @fileoverview Enhanced DND Persistence Service with Dependency Injection
 * @description Comprehensive DND validation persistence service with robust error handling and monitoring
 * <AUTHOR> System
 * @version 2.0.0
 */

import {
  DNDValidation,
  CreateDNDValidationRequest,
  DNDValidationStats,
  DNDValidationQueryOptions,
  DNDValidationQueryResult,
  DNDPersistenceOptions,
  DNDHealthCheckResult,
  IDNDPersistenceService,
  IDNDValidationRepository,
  IDNDAuditLogger,
  IDNDValidator
} from '../types';
import { DNDValidationModel } from '../database/models/DNDValidation';
import { DNDCheckResult, BulkDNDResponse } from './dndService';
import { normalizePhoneNumber } from '../utils/phoneNormalizer';
import { createDNDPersistenceConfig } from '../config/dndPersistence';
import { DNDErrorFactory } from '../utils/dndPersistenceErrors';
import { dndValidator } from '../utils/dndValidator';
import { dndAuditLogger } from '../utils/dndAuditLogger';
import logger from '../utils/logger';

/**
 * Enhanced DND Persistence Service with dependency injection and comprehensive error handling
 * @implements {IDNDPersistenceService}
 */
export class DNDPersistenceService implements IDNDPersistenceService {
  private readonly repository: IDNDValidationRepository;
  private readonly auditLogger: IDNDAuditLogger;
  private readonly validator: IDNDValidator;
  private readonly config: ReturnType<typeof createDNDPersistenceConfig>;

  /**
   * Creates a new DNDPersistenceService with dependency injection
   * @param {IDNDValidationRepository} repository - Database repository for DND validations
   * @param {IDNDAuditLogger} auditLogger - Audit logger for compliance tracking
   * @param {IDNDValidator} validator - Input validator and sanitizer
   * @param {string} environment - Environment configuration (development, production, test)
   */
  constructor(
    repository?: IDNDValidationRepository,
    auditLogger?: IDNDAuditLogger,
    validator?: IDNDValidator,
    environment: string = process.env.NODE_ENV || 'development'
  ) {
    this.repository = repository || new DNDValidationModel();
    this.auditLogger = auditLogger || dndAuditLogger;
    this.validator = validator || dndValidator;
    this.config = createDNDPersistenceConfig(environment);

    logger.info('DNDPersistenceService initialized', {
      environment,
      auditLogging: this.config.auditLogging,
      performanceMonitoring: this.config.performanceMonitoring
    });
  }
  /**
   * Saves a single DND validation result to the database with comprehensive error handling
   * @param {DNDCheckResult} result - DND validation result to save
   * @param {DNDPersistenceOptions} options - Persistence options and configuration
   * @returns {Promise<DNDValidation>} Saved validation record
   * @throws {InvalidPhoneNumberError} When phone number format is invalid
   * @throws {BulkSizeExceededError} When validation data exceeds size limits
   * @throws {RecentValidationExistsError} When recent validation exists and skipRecentValidations is false
   *
   * @example
   * ```typescript
   * const service = new DNDPersistenceService();
   * const validation = await service.saveDNDValidation({
   *   phone: '9876543210',
   *   dnd_status: 'DND'
   * }, {
   *   skipRecentValidations: true,
   *   includeMetadata: true
   * });
   * ```
   */
  async saveDNDValidation(
    result: DNDCheckResult,
    options: DNDPersistenceOptions = {}
  ): Promise<DNDValidation> {
    const startTime = Date.now();
    const {
      skipRecentValidations = false,
      recentValidationHours = this.config.recentValidationHours,
      includeMetadata = true,
      enableAuditLog = this.config.auditLogging,
      userId,
      source = 'dnd-persistence-service'
    } = options;

    // Normalize the phone number for consistent storage
    const normalizedResult = normalizePhoneNumber(result.phone);
    const phoneNumber = normalizedResult.isValid ? normalizedResult.normalized : result.phone;

    try {
      // Validate input data
      const phoneValidation = this.validator.validatePhoneNumber(result.phone);
      if (!phoneValidation.isValid) {
        throw DNDErrorFactory.fromValidationFailure('phoneNumber', result.phone, phoneValidation.error!);
      }

      const statusValidation = this.validator.validateDNDStatus(result.dnd_status);
      if (!statusValidation.isValid) {
        throw DNDErrorFactory.fromValidationFailure('dndStatus', result.dnd_status, statusValidation.error!);
      }

      // Check if we should skip recent validations
      if (skipRecentValidations) {
        const isRecent = await this.repository.isRecentlyValidated(phoneNumber, recentValidationHours);
        if (isRecent) {
          logger.debug(`Skipping recent validation for phone: ${phoneNumber}`);
          const existing = await this.repository.findByPhone(phoneNumber);
          if (existing) {
            if (enableAuditLog) {
              await this.auditLogger.logEvent('validation_skipped', {
                phoneNumber,
                reason: 'recent_validation_exists'
              }, userId);
            }
            return existing;
          }
        }
      }

      // Prepare validation metadata
      const validationMetadata: Record<string, any> = {};
      
      if (includeMetadata) {
        validationMetadata.originalPhone = result.phone;
        validationMetadata.normalizedPhone = phoneNumber;
        validationMetadata.isValidFormat = normalizedResult.isValid;
        
        if (result.name) {
          validationMetadata.name = result.name;
        }
        
        if (result.email) {
          validationMetadata.email = result.email;
        }
        
        if (result.error_message) {
          validationMetadata.errorMessage = result.error_message;
        }
        
        if (!normalizedResult.isValid && normalizedResult.error) {
          validationMetadata.normalizationError = normalizedResult.error;
        }
      }

      const validationData: CreateDNDValidationRequest = {
        phoneNumber,
        dndStatus: result.dnd_status,
        validatedAt: new Date(),
        validationMetadata
      };

      const savedValidation = await this.repository.upsert(validationData);

      // Log audit event if enabled
      if (enableAuditLog) {
        await this.auditLogger.logValidationCreated(savedValidation, userId);
      }

      const processingTime = Date.now() - startTime;
      logger.info(`Saved DND validation for phone: ${phoneNumber}, status: ${result.dnd_status}`, {
        phoneNumber,
        dndStatus: result.dnd_status,
        processingTimeMs: processingTime,
        source
      });

      return savedValidation;

    } catch (error) {
      logger.error(`Error saving DND validation for phone ${phoneNumber}:`, error);
      throw error;
    }
  }

  /**
   * Saves multiple DND validation results to the database with bulk operations
   * @param {DNDCheckResult[]} results - Array of DND validation results to save
   * @param {DNDPersistenceOptions} options - Persistence options and configuration
   * @returns {Promise<DNDValidation[]>} Array of saved validation records
   * @throws {BulkSizeExceededError} When bulk operation exceeds maximum size
   * @throws {InvalidPhoneNumberError} When any phone number format is invalid
   *
   * @example
   * ```typescript
   * const service = new DNDPersistenceService();
   * const validations = await service.saveBulkDNDValidations([
   *   { phone: '9876543210', dnd_status: 'DND' },
   *   { phone: '9876543211', dnd_status: 'Non-DND' }
   * ], {
   *   skipRecentValidations: true,
   *   includeMetadata: true
   * });
   * ```
   */
  async saveBulkDNDValidations(
    results: DNDCheckResult[],
    options: DNDPersistenceOptions = {}
  ): Promise<DNDValidation[]> {
    const startTime = Date.now();
    const {
      skipRecentValidations = false,
      recentValidationHours = this.config.recentValidationHours,
      includeMetadata = true,
      enableAuditLog = this.config.auditLogging,
      userId,
      source = 'dnd-persistence-service'
    } = options;

    if (results.length === 0) {
      return [];
    }

    try {
      logger.info(`Saving ${results.length} DND validation results to database`);

      // Prepare validation data for bulk insert
      const validationDataList: CreateDNDValidationRequest[] = [];
      const skippedPhones: string[] = [];

      for (const result of results) {
        // Normalize the phone number for consistent storage
        const normalizedResult = normalizePhoneNumber(result.phone);
        const phoneNumber = normalizedResult.isValid ? normalizedResult.normalized : result.phone;

        // Validate input data
        const phoneValidation = this.validator.validatePhoneNumber(result.phone);
        if (!phoneValidation.isValid) {
          logger.warn(`Skipping invalid phone number: ${result.phone}`, { error: phoneValidation.error });
          continue;
        }

        const statusValidation = this.validator.validateDNDStatus(result.dnd_status);
        if (!statusValidation.isValid) {
          logger.warn(`Skipping invalid DND status: ${result.dnd_status}`, { error: statusValidation.error });
          continue;
        }

        // Check if we should skip recent validations
        if (skipRecentValidations) {
          const isRecent = await this.repository.isRecentlyValidated(phoneNumber, recentValidationHours);
          if (isRecent) {
            skippedPhones.push(phoneNumber);
            continue;
          }
        }

        // Prepare validation metadata
        const validationMetadata: Record<string, any> = {};
        
        if (includeMetadata) {
          validationMetadata.originalPhone = result.phone;
          validationMetadata.normalizedPhone = phoneNumber;
          validationMetadata.isValidFormat = normalizedResult.isValid;
          
          if (result.name) {
            validationMetadata.name = result.name;
          }
          
          if (result.email) {
            validationMetadata.email = result.email;
          }
          
          if (result.error_message) {
            validationMetadata.errorMessage = result.error_message;
          }
          
          if (!normalizedResult.isValid && normalizedResult.error) {
            validationMetadata.normalizationError = normalizedResult.error;
          }
        }

        validationDataList.push({
          phoneNumber,
          dndStatus: result.dnd_status,
          validatedAt: new Date(),
          validationMetadata
        });
      }

      if (skippedPhones.length > 0) {
        logger.info(`Skipped ${skippedPhones.length} recent validations`);
      }

      if (validationDataList.length === 0) {
        logger.info('No new validations to save (all were recent)');
        return [];
      }

      // Perform bulk upsert
      const savedValidations = await this.repository.bulkUpsert(validationDataList);

      // Log audit event if enabled
      if (enableAuditLog) {
        await this.auditLogger.logBulkOperation('bulk_validation_created', savedValidations.length, userId);
      }

      const processingTime = Date.now() - startTime;
      logger.info(`Successfully saved ${savedValidations.length} DND validations to database`, {
        savedCount: savedValidations.length,
        skippedCount: skippedPhones.length,
        processingTimeMs: processingTime,
        source
      });

      return savedValidations;

    } catch (error) {
      logger.error('Error saving bulk DND validations:', error);
      throw error;
    }
  }

  /**
   * Saves DND validation results from a bulk response
   * @param {BulkDNDResponse} bulkResponse - Bulk DND response containing validation results
   * @param {DNDPersistenceOptions} options - Persistence options and configuration
   * @returns {Promise<DNDValidation[]>} Array of saved validation records
   */
  async saveBulkDNDResponse(
    bulkResponse: BulkDNDResponse,
    options: DNDPersistenceOptions = {}
  ): Promise<DNDValidation[]> {
    return this.saveBulkDNDValidations(bulkResponse.results, options);
  }

  /**
   * Retrieves a DND validation by phone number
   * @param {string} phoneNumber - Phone number to lookup
   * @returns {Promise<DNDValidation | null>} Validation record or null if not found
   * @throws {InvalidPhoneNumberError} When phone number format is invalid
   */
  async getDNDValidation(phoneNumber: string): Promise<DNDValidation | null> {
    try {
      // Validate and normalize phone number
      const phoneValidation = this.validator.validatePhoneNumber(phoneNumber);
      if (!phoneValidation.isValid) {
        throw DNDErrorFactory.fromValidationFailure('phoneNumber', phoneNumber, phoneValidation.error!);
      }

      const lookupPhone = phoneValidation.sanitized!;
      return await this.repository.findByPhone(lookupPhone);
    } catch (error) {
      logger.error(`Error getting DND validation for phone ${phoneNumber}:`, error);
      throw error;
    }
  }

  /**
   * Checks if a phone number has been validated recently
   * @param {string} phoneNumber - Phone number to check
   * @param {number} withinHours - Hours to check within (default: 24)
   * @returns {Promise<boolean>} True if recently validated
   * @throws {InvalidPhoneNumberError} When phone number format is invalid
   */
  async isRecentlyValidated(phoneNumber: string, withinHours: number = 24): Promise<boolean> {
    try {
      // Validate and normalize phone number
      const phoneValidation = this.validator.validatePhoneNumber(phoneNumber);
      if (!phoneValidation.isValid) {
        throw DNDErrorFactory.fromValidationFailure('phoneNumber', phoneNumber, phoneValidation.error!);
      }

      const lookupPhone = phoneValidation.sanitized!;
      return await this.repository.isRecentlyValidated(lookupPhone, withinHours);
    } catch (error) {
      logger.error(`Error checking recent validation for phone ${phoneNumber}:`, error);
      throw error;
    }
  }

  /**
   * Retrieves DND validation statistics
   * @returns {Promise<DNDValidationStats>} Validation statistics
   * @throws {DatabaseConnectionError} When database connection fails
   */
  async getValidationStats(): Promise<DNDValidationStats> {
    try {
      return await this.repository.getStats();
    } catch (error) {
      logger.error('Error getting DND validation stats:', error);
      throw error;
    }
  }

  /**
   * Retrieves paginated DND validations with filtering
   * @param {DNDValidationQueryOptions} options - Query options for filtering and pagination
   * @returns {Promise<DNDValidationQueryResult>} Paginated validation results
   * @throws {DatabaseConnectionError} When database connection fails
   */
  async getDNDValidations(options: DNDValidationQueryOptions = {}): Promise<DNDValidationQueryResult> {
    try {
      // Validate pagination parameters if provided
      if (options.page !== undefined || options.limit !== undefined) {
        const paginationValidation = this.validator.validatePagination(
          options.page || 1,
          options.limit || 10
        );
        if (!paginationValidation.isValid) {
          throw DNDErrorFactory.fromValidationFailure('pagination', options, paginationValidation.error!);
        }
      }

      return await this.repository.findAll(options);
    } catch (error) {
      logger.error('Error getting DND validations:', error);
      throw error;
    }
  }

  /**
   * Deletes a DND validation by phone number
   * @param {string} phoneNumber - Phone number to delete
   * @returns {Promise<void>}
   * @throws {InvalidPhoneNumberError} When phone number format is invalid
   * @throws {ValidationNotFoundError} When validation record is not found
   */
  async deleteDNDValidation(phoneNumber: string): Promise<void> {
    try {
      // Validate and normalize phone number
      const phoneValidation = this.validator.validatePhoneNumber(phoneNumber);
      if (!phoneValidation.isValid) {
        throw DNDErrorFactory.fromValidationFailure('phoneNumber', phoneNumber, phoneValidation.error!);
      }

      const lookupPhone = phoneValidation.sanitized!;
      await this.repository.delete(lookupPhone);

      // Log audit event
      if (this.config.auditLogging) {
        await this.auditLogger.logValidationDeleted(lookupPhone);
      }

      logger.info(`Deleted DND validation for phone: ${lookupPhone}`);
    } catch (error) {
      logger.error(`Error deleting DND validation for phone ${phoneNumber}:`, error);
      throw error;
    }
  }

  /**
   * Cleans up old DND validations (older than specified days)
   * @param {number} olderThanDays - Number of days to retain validations (default: 90)
   * @returns {Promise<number>} Number of deleted validation records
   * @throws {DatabaseConnectionError} When database connection fails
   */
  async cleanupOldValidations(olderThanDays: number = this.config.cleanup.retentionDays): Promise<number> {
    const startTime = Date.now();

    try {
      // Validate input
      if (typeof olderThanDays !== 'number' || olderThanDays < 1) {
        throw DNDErrorFactory.fromValidationFailure('olderThanDays', olderThanDays, 'Must be a positive number');
      }

      const batchSize = this.config.cleanup.batchSize;
      let totalDeleted = 0;
      let hasMore = true;

      while (hasMore) {
        const result = await this.repository.findAll({
          page: 1,
          limit: batchSize,
          endDate: new Date(Date.now() - (olderThanDays * 24 * 60 * 60 * 1000))
        });

        if (result.validations.length === 0) {
          hasMore = false;
          break;
        }

        // Delete validations in this batch
        for (const validation of result.validations) {
          await this.repository.delete(validation.phoneNumber);
          totalDeleted++;
        }

        // If we got fewer results than the batch size, we're done
        if (result.validations.length < batchSize) {
          hasMore = false;
        }
      }

      // Log audit event
      if (this.config.auditLogging && totalDeleted > 0) {
        await this.auditLogger.logCleanupOperation(totalDeleted, olderThanDays);
      }

      const processingTime = Date.now() - startTime;
      logger.info(`Cleaned up ${totalDeleted} old DND validations (older than ${olderThanDays} days)`, {
        deletedCount: totalDeleted,
        retentionDays: olderThanDays,
        processingTimeMs: processingTime
      });

      return totalDeleted;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      logger.error('Error cleaning up old DND validations:', {
        olderThanDays,
        processingTimeMs: processingTime,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Performs health check on the DND persistence system
   * @returns {Promise<DNDHealthCheckResult>} Health check results
   */
  async performHealthCheck(): Promise<DNDHealthCheckResult> {
    try {
      return await this.repository.healthCheck();
    } catch (error) {
      logger.error('Health check failed:', error);
      throw error;
    }
  }

  /**
   * Gets the current configuration
   * @returns {ReturnType<typeof createDNDPersistenceConfig>} Current configuration
   */
  getConfig(): ReturnType<typeof createDNDPersistenceConfig> {
    return { ...this.config };
  }

  /**
   * Updates service configuration (for testing purposes)
   * @param {Partial<ReturnType<typeof createDNDPersistenceConfig>>} newConfig - New configuration values
   */
  updateConfig(newConfig: Partial<ReturnType<typeof createDNDPersistenceConfig>>): void {
    Object.assign(this.config, newConfig);
    logger.info('DNDPersistenceService configuration updated', { newConfig });
  }
}

/**
 * Singleton instance of DNDPersistenceService for application use
 * @constant {DNDPersistenceService}
 */
export const dndPersistenceService = new DNDPersistenceService();

export default DNDPersistenceService;
