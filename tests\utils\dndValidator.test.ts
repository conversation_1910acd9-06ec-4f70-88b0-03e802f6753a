/**
 * @fileoverview Comprehensive tests for DND Validator
 * @description Tests for input validation, sanitization, and edge cases
 */

import { DNDValidator } from '@/utils/dndValidator';
import { 
  InvalidPhoneNumberError, 
  InvalidDNDStatusError, 
  InvalidMetadataError 
} from '@/utils/dndPersistenceErrors';

describe('DNDValidator', () => {
  let validator: DNDValidator;

  beforeEach(() => {
    validator = new DNDValidator();
  });

  describe('validatePhoneNumber', () => {
    describe('valid phone numbers', () => {
      const validPhones = [
        '9876543210',
        '+919876543210',
        '919876543210',
        '09876543210',
        '6123456789', // Different starting digit
        '7987654321',
        '8765432109',
        '9012345678'
      ];

      validPhones.forEach(phone => {
        it(`should validate ${phone} as valid`, () => {
          const result = validator.validatePhoneNumber(phone);
          expect(result.isValid).toBe(true);
          expect(result.sanitized).toBeDefined();
          expect(result.sanitized).toMatch(/^\d{10}$/);
        });
      });
    });

    describe('invalid phone numbers', () => {
      const invalidPhones = [
        '',
        '   ',
        null,
        undefined,
        '123',
        '12345',
        '123456789', // 9 digits
        '12345678901', // 11 digits
        '5123456789', // Invalid starting digit
        '0123456789', // Invalid starting digit after normalization
        'abcdefghij',
        '98765-43210',
        '+1234567890', // Non-Indian international
        '+44987654321'
      ];

      invalidPhones.forEach(phone => {
        it(`should validate ${phone} as invalid`, () => {
          const result = validator.validatePhoneNumber(phone as any);
          expect(result.isValid).toBe(false);
          expect(result.error).toBeDefined();
        });
      });
    });

    describe('edge cases', () => {
      it('should handle very long strings', () => {
        const longPhone = '9'.repeat(100);
        const result = validator.validatePhoneNumber(longPhone);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('length');
      });

      it('should handle special characters', () => {
        const result = validator.validatePhoneNumber('98765@43210');
        expect(result.isValid).toBe(false);
      });

      it('should handle unicode characters', () => {
        const result = validator.validatePhoneNumber('9876543२१०');
        expect(result.isValid).toBe(false);
      });
    });
  });

  describe('validateDNDStatus', () => {
    describe('valid statuses', () => {
      const validStatuses = ['DND', 'Non-DND', 'Error'];

      validStatuses.forEach(status => {
        it(`should validate ${status} as valid`, () => {
          const result = validator.validateDNDStatus(status);
          expect(result.isValid).toBe(true);
          expect(result.sanitized).toBe(status);
        });
      });

      it('should trim whitespace', () => {
        const result = validator.validateDNDStatus('  DND  ');
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toBe('DND');
      });
    });

    describe('invalid statuses', () => {
      const invalidStatuses = [
        '',
        '   ',
        null,
        undefined,
        'INVALID',
        'dnd', // Case sensitive
        'non-dnd',
        'error',
        'YES',
        'NO',
        123
      ];

      invalidStatuses.forEach(status => {
        it(`should validate ${status} as invalid`, () => {
          const result = validator.validateDNDStatus(status as any);
          expect(result.isValid).toBe(false);
          expect(result.error).toBeDefined();
        });
      });
    });
  });

  describe('validateMetadata', () => {
    describe('valid metadata', () => {
      it('should validate null metadata', () => {
        const result = validator.validateMetadata(null);
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toEqual({});
      });

      it('should validate undefined metadata', () => {
        const result = validator.validateMetadata(undefined);
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toEqual({});
      });

      it('should validate empty object', () => {
        const result = validator.validateMetadata({});
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toEqual({});
      });

      it('should validate simple object', () => {
        const metadata = { name: 'John Doe', email: '<EMAIL>' };
        const result = validator.validateMetadata(metadata);
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toEqual(metadata);
      });

      it('should validate nested objects', () => {
        const metadata = {
          user: { name: 'John', age: 30 },
          settings: { theme: 'dark' }
        };
        const result = validator.validateMetadata(metadata);
        expect(result.isValid).toBe(true);
        expect(result.sanitized).toBeDefined();
      });
    });

    describe('invalid metadata', () => {
      it('should reject arrays', () => {
        const result = validator.validateMetadata([1, 2, 3]);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('object');
      });

      it('should reject strings', () => {
        const result = validator.validateMetadata('invalid');
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('object');
      });

      it('should reject numbers', () => {
        const result = validator.validateMetadata(123);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('object');
      });

      it('should reject oversized metadata', () => {
        const largeMetadata = {
          data: 'x'.repeat(20000) // Exceeds 10KB limit
        };
        const result = validator.validateMetadata(largeMetadata);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('size');
      });

      it('should reject too many keys', () => {
        const manyKeys: Record<string, any> = {};
        for (let i = 0; i < 100; i++) {
          manyKeys[`key${i}`] = `value${i}`;
        }
        const result = validator.validateMetadata(manyKeys);
        expect(result.isValid).toBe(false);
        expect(result.error).toContain('keys');
      });
    });
  });

  describe('sanitizePhoneNumber', () => {
    it('should remove non-digit characters except +', () => {
      const result = validator.sanitizePhoneNumber('98-76-54-32-10');
      expect(result).toBe('9876543210');
    });

    it('should handle country codes', () => {
      expect(validator.sanitizePhoneNumber('+919876543210')).toBe('9876543210');
      expect(validator.sanitizePhoneNumber('919876543210')).toBe('9876543210');
      expect(validator.sanitizePhoneNumber('09876543210')).toBe('9876543210');
    });

    it('should preserve international numbers', () => {
      const result = validator.sanitizePhoneNumber('+1234567890');
      expect(result).toBe('+1234567890');
    });

    it('should handle malformed input gracefully', () => {
      expect(() => validator.sanitizePhoneNumber('invalid')).not.toThrow();
    });
  });

  describe('sanitizeMetadata', () => {
    it('should remove dangerous keys', () => {
      const metadata = {
        name: 'John',
        __proto__: { malicious: true },
        constructor: 'hack',
        prototype: 'attack'
      };
      const result = validator.sanitizeMetadata(metadata);
      expect(result).toEqual({ name: 'John' });
    });

    it('should sanitize string values', () => {
      const metadata = {
        name: '<script>alert("xss")</script>John',
        description: 'javascript:void(0)'
      };
      const result = validator.sanitizeMetadata(metadata);
      expect(result.name).not.toContain('<script>');
      expect(result.description).not.toContain('javascript:');
    });

    it('should handle nested objects with depth limit', () => {
      const metadata = {
        level1: {
          level2: {
            level3: {
              level4: 'too deep'
            }
          }
        }
      };
      const result = validator.sanitizeMetadata(metadata);
      expect(result.level1).toBeDefined();
      expect(result.level1.level2).toBeDefined();
      expect(result.level1.level2.level3).toBeUndefined();
    });

    it('should preserve valid data types', () => {
      const metadata = {
        string: 'text',
        number: 42,
        boolean: true,
        date: new Date(),
        array: [1, 2, 3]
      };
      const result = validator.sanitizeMetadata(metadata);
      expect(result.string).toBe('text');
      expect(result.number).toBe(42);
      expect(result.boolean).toBe(true);
      expect(typeof result.date).toBe('string'); // Converted to ISO string
      expect(Array.isArray(result.array)).toBe(true);
    });
  });

  describe('validateBulkSize', () => {
    it('should validate normal bulk sizes', () => {
      const result = validator.validateBulkSize(100, 1000);
      expect(result.isValid).toBe(true);
    });

    it('should reject oversized bulks', () => {
      const result = validator.validateBulkSize(2000, 1000);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('exceeds');
    });

    it('should reject empty bulks', () => {
      const result = validator.validateBulkSize(0, 1000);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('empty');
    });

    it('should reject negative sizes', () => {
      const result = validator.validateBulkSize(-1, 1000);
      expect(result.isValid).toBe(false);
      expect(result.error).toContain('non-negative');
    });
  });

  describe('validatePagination', () => {
    it('should validate normal pagination', () => {
      const result = validator.validatePagination(1, 10);
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toEqual({ page: 1, limit: 10 });
    });

    it('should reject invalid page numbers', () => {
      expect(validator.validatePagination(0, 10).isValid).toBe(false);
      expect(validator.validatePagination(-1, 10).isValid).toBe(false);
    });

    it('should reject invalid limits', () => {
      expect(validator.validatePagination(1, 0).isValid).toBe(false);
      expect(validator.validatePagination(1, -1).isValid).toBe(false);
      expect(validator.validatePagination(1, 2000).isValid).toBe(false);
    });

    it('should floor decimal values', () => {
      const result = validator.validatePagination(1.7, 10.9);
      expect(result.isValid).toBe(true);
      expect(result.sanitized).toEqual({ page: 1, limit: 10 });
    });
  });

  describe('performance tests', () => {
    it('should validate phone numbers quickly', () => {
      const start = Date.now();
      for (let i = 0; i < 1000; i++) {
        validator.validatePhoneNumber('9876543210');
      }
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(100); // Should complete in under 100ms
    });

    it('should sanitize metadata efficiently', () => {
      const largeMetadata = {
        data: 'x'.repeat(5000),
        nested: {
          field1: 'value1',
          field2: 'value2'
        }
      };
      
      const start = Date.now();
      for (let i = 0; i < 100; i++) {
        validator.sanitizeMetadata(largeMetadata);
      }
      const duration = Date.now() - start;
      expect(duration).toBeLessThan(1000); // Should complete in under 1 second
    });
  });

  describe('error handling', () => {
    it('should handle null input gracefully', () => {
      expect(() => validator.validatePhoneNumber(null as any)).not.toThrow();
      expect(() => validator.validateDNDStatus(null as any)).not.toThrow();
      expect(() => validator.sanitizePhoneNumber(null as any)).not.toThrow();
    });

    it('should handle undefined input gracefully', () => {
      expect(() => validator.validatePhoneNumber(undefined as any)).not.toThrow();
      expect(() => validator.validateDNDStatus(undefined as any)).not.toThrow();
      expect(() => validator.sanitizePhoneNumber(undefined as any)).not.toThrow();
    });

    it('should handle circular references in metadata', () => {
      const circular: any = { name: 'test' };
      circular.self = circular;

      expect(() => validator.validateMetadata(circular)).not.toThrow();
    });
  });
});
