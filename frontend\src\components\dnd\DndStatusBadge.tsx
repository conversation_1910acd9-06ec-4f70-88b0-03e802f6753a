import React from 'react';
import { AlertCircle, CheckCircle, XCircle } from 'lucide-react';

interface DndStatusBadgeProps {
  status: 'DND' | 'Non-DND' | 'Error';
  showIcon?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

export const DndStatusBadge: React.FC<DndStatusBadgeProps> = ({
  status,
  showIcon = true,
  size = 'md'
}) => {
  const getStatusConfig = () => {
    switch (status) {
      case 'DND':
        return {
          icon: <XCircle className="h-3 w-3" />,
          text: 'DND',
          className: 'bg-red-100 text-red-800 border border-red-200',
        };
      case 'Non-DND':
        return {
          icon: <CheckCircle className="h-3 w-3" />,
          text: 'Non-DND',
          className: 'bg-green-100 text-green-800 border border-green-200',
        };
      case 'Error':
        return {
          icon: <AlertCircle className="h-3 w-3" />,
          text: 'Error',
          className: 'bg-yellow-100 text-yellow-800 border border-yellow-200',
        };
      default:
        return {
          icon: <AlertCircle className="h-3 w-3" />,
          text: 'Unknown',
          className: 'bg-gray-100 text-gray-800 border border-gray-200',
        };
    }
  };

  const config = getStatusConfig();

  return (
    <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}>
      {showIcon && (
        <span className="mr-1">
          {config.icon}
        </span>
      )}
      {config.text}
    </span>
  );
};
