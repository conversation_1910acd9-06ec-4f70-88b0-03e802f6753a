import WebSocket from 'ws';
import { AudioProcessor } from './audio-processor';
import { ConversationEngine } from './conversation-engine';
import { CallModel } from '../../database/models/Call';
import { LeadModel } from '../../database/models/Lead';
import logger from '../../utils/logger';

export interface SessionState {
  callSid: string;
  leadId?: number;
  status: 'initializing' | 'active' | 'ending' | 'ended';
  startTime: Date;
  lastActivity: Date;
  audioChunksReceived: number;
  audioChunksSent: number;
  conversationTurns: number;
  transcript: Array<{
    speaker: 'customer' | 'ai';
    text: string;
    timestamp: Date;
    confidence?: number;
  }>;
}

export class StreamingSession {
  public readonly callSid: string;
  private ws: WebSocket;
  private audioProcessor: AudioProcessor;
  private conversationEngine: ConversationEngine;
  private state: SessionState;
  private audioBuffer: Buffer[] = [];
  private isProcessingAudio = false;
  private callRecord?: any;

  constructor(
    callSid: string,
    ws: WebSocket,
    audioProcessor: AudioProcessor,
    conversationEngine: ConversationEngine
  ) {
    this.callSid = callSid;
    this.ws = ws;
    this.audioProcessor = audioProcessor;
    this.conversationEngine = conversationEngine;
    
    this.state = {
      callSid,
      status: 'initializing',
      startTime: new Date(),
      lastActivity: new Date(),
      audioChunksReceived: 0,
      audioChunksSent: 0,
      conversationTurns: 0,
      transcript: []
    };
  }

  async initialize(): Promise<void> {
    try {
      logger.info(`🎙️ Initializing streaming session for call: ${this.callSid}`);

      // Try to find existing call record or create new one
      this.callRecord = await CallModel.findByCallSid(this.callSid);
      if (this.callRecord) {
        this.state.leadId = this.callRecord.leadId;
        logger.info(`📞 Found existing call record for lead: ${this.state.leadId}`);
      } else {
        // Create new call record
        this.callRecord = await CallModel.create({
          leadId: this.state.leadId || 0, // Default to 0 if no lead
          callSid: this.callSid,
          status: 'in-progress',
          direction: 'outbound',
          initialMessage: 'Real-time streaming call'
        });
        logger.info(`📞 Created new call record: ${this.callRecord.id}`);
      }

      // Initialize conversation engine
      await this.conversationEngine.initializeSession(this.callSid, this.state.leadId);

      // Send initial AI greeting
      await this.sendInitialGreeting();

      this.state.status = 'active';
      logger.info(`✅ Streaming session initialized for call: ${this.callSid}`);

    } catch (error) {
      logger.error('❌ Error initializing streaming session:', error);
      this.state.status = 'ended';
      throw error;
    }
  }

  async processAudioChunk(audioBuffer: Buffer): Promise<void> {
    if (this.state.status !== 'active') {
      logger.warn(`❌ Received audio chunk for inactive session: ${this.callSid}`);
      return;
    }

    this.state.audioChunksReceived++;
    this.state.lastActivity = new Date();

    // Add to buffer
    this.audioBuffer.push(audioBuffer);

    // Process audio if not already processing and we have enough data
    if (!this.isProcessingAudio && this.shouldProcessAudio()) {
      await this.processBufferedAudio();
    }
  }

  private shouldProcessAudio(): boolean {
    // Process when we have at least 500ms of audio (4 chunks of 100ms each)
    return this.audioBuffer.length >= 4;
  }

  private async processBufferedAudio(): Promise<void> {
    if (this.isProcessingAudio || this.audioBuffer.length === 0) {
      return;
    }

    this.isProcessingAudio = true;

    try {
      // Combine buffered audio chunks
      const combinedAudio = Buffer.concat(this.audioBuffer);
      this.audioBuffer = []; // Clear buffer

      logger.debug(`🎤 Processing ${combinedAudio.length} bytes of audio for call: ${this.callSid}`);

      // Convert audio to text
      const transcription = await this.audioProcessor.speechToText(combinedAudio);
      
      if (transcription && transcription.text.trim()) {
        logger.info(`🎤 Customer said: "${transcription.text}"`);
        
        // Add to transcript
        this.state.transcript.push({
          speaker: 'customer',
          text: transcription.text,
          timestamp: new Date(),
          confidence: transcription.confidence
        });

        // Generate AI response
        const aiResponse = await this.conversationEngine.processInput(
          this.callSid,
          transcription.text,
          this.getConversationContext()
        );

        if (aiResponse) {
          logger.info(`🤖 AI responded: "${aiResponse}"`);
          
          // Add AI response to transcript
          this.state.transcript.push({
            speaker: 'ai',
            text: aiResponse,
            timestamp: new Date()
          });

          // Convert AI response to speech and send back
          await this.sendAIResponse(aiResponse);
          
          this.state.conversationTurns++;
        }
      }

    } catch (error) {
      logger.error('❌ Error processing buffered audio:', error);
      await this.sendAIResponse("I'm sorry, I didn't catch that. Could you please repeat?");
    } finally {
      this.isProcessingAudio = false;
    }
  }

  private async sendInitialGreeting(): Promise<void> {
    let greeting = "Hello! This is an AI assistant. How can I help you today?";

    // Get personalized greeting if we have lead info
    if (this.state.leadId) {
      try {
        const lead = await LeadModel.findById(this.state.leadId);
        if (lead) {
          greeting = await this.conversationEngine.generatePersonalizedGreeting(lead);
        }
      } catch (error) {
        logger.warn('❌ Error getting personalized greeting:', error);
      }
    }

    await this.sendAIResponse(greeting);
  }

  private async sendAIResponse(text: string): Promise<void> {
    try {
      // Convert text to speech
      let audioBuffer = await this.audioProcessor.textToSpeech(text);

      // 🔥 BEST PRACTICE: Ensure audio chunks meet Exotel requirements
      audioBuffer = this.audioProcessor.prepareAudioForExotel(audioBuffer);

      // Convert to base64 for WebSocket transmission
      const base64Audio = audioBuffer.toString('base64');

      // Send audio back to Exotel
      if (this.ws.readyState === WebSocket.OPEN) {
        this.ws.send(JSON.stringify({
          event: 'media',
          payload: base64Audio
        }));

        this.state.audioChunksSent++;
        logger.debug(`🔊 Sent AI audio response (${audioBuffer.length} bytes) - Exotel compliant`);
      } else {
        logger.warn('❌ WebSocket not open, cannot send audio response');
      }

    } catch (error) {
      logger.error('❌ Error sending AI response:', error);
    }
  }

  async handleDTMF(digit: string): Promise<void> {
    logger.info(`📞 DTMF received: ${digit} for call: ${this.callSid}`);
    
    // Handle common DTMF patterns
    switch (digit) {
      case '0':
        await this.sendAIResponse("Connecting you to a human agent. Please hold.");
        await this.requestAgentTransfer();
        break;
      case '*':
        await this.sendAIResponse("Let me repeat that information.");
        await this.repeatLastResponse();
        break;
      case '#':
        await this.sendAIResponse("Thank you for calling. Have a great day!");
        await this.end();
        break;
      default:
        // Pass DTMF to conversation engine for custom handling
        await this.conversationEngine.handleDTMF(this.callSid, digit);
    }
  }

  async handleMark(mark: string): Promise<void> {
    logger.info(`📍 Mark received: ${mark} for call: ${this.callSid}`);
    
    // Handle custom marks for debugging or flow control
    if (mark.startsWith('debug:')) {
      logger.debug(`🐛 Debug mark: ${mark}`);
    } else if (mark.startsWith('flow:')) {
      await this.conversationEngine.handleFlowMark(this.callSid, mark);
    }
  }

  async clearContext(): Promise<void> {
    logger.info(`🔄 Clearing context for call: ${this.callSid}`);
    
    // Clear conversation context
    await this.conversationEngine.clearContext(this.callSid);
    
    // Reset session state
    this.state.transcript = [];
    this.state.conversationTurns = 0;
    this.audioBuffer = [];
    
    // Send fresh greeting
    await this.sendAIResponse("Let's start fresh. How can I help you today?");
  }

  private getConversationContext(): any {
    return {
      callSid: this.callSid,
      leadId: this.state.leadId,
      conversationTurns: this.state.conversationTurns,
      transcript: this.state.transcript,
      sessionDuration: Date.now() - this.state.startTime.getTime()
    };
  }

  private async requestAgentTransfer(): Promise<void> {
    // Mark session for agent transfer
    this.state.status = 'ending';
    
    // Close WebSocket to trigger Exotel's next applet (Passthru → Connect)
    if (this.ws.readyState === WebSocket.OPEN) {
      this.ws.close(1000, 'Agent transfer requested');
    }
  }

  private async repeatLastResponse(): Promise<void> {
    const lastAIResponse = this.state.transcript
      .filter(entry => entry.speaker === 'ai')
      .pop();
    
    if (lastAIResponse) {
      await this.sendAIResponse(lastAIResponse.text);
    } else {
      await this.sendAIResponse("I haven't said anything yet. How can I help you?");
    }
  }

  async end(): Promise<void> {
    if (this.state.status === 'ended') {
      return;
    }

    logger.info(`🛑 Ending streaming session for call: ${this.callSid}`);
    this.state.status = 'ending';

    try {
      // Save final call data
      if (this.callRecord) {
        await CallModel.update(this.callRecord.id, {
          status: 'completed',
          transcript: JSON.stringify(this.state.transcript),
          duration: Math.floor((Date.now() - this.state.startTime.getTime()) / 1000)
        });
      }

      // End conversation session
      await this.conversationEngine.endSession(this.callSid);

      // 🔥 BEST PRACTICE: Properly disconnect WebSocket to allow call flow to proceed
      if (this.ws.readyState === WebSocket.OPEN) {
        logger.info(`🔌 Disconnecting WebSocket for call: ${this.callSid} to allow flow continuation`);
        this.ws.close(1000, 'Conversation completed - proceeding to next applet');
      }

      this.state.status = 'ended';
      logger.info(`✅ Streaming session ended for call: ${this.callSid}`);

    } catch (error) {
      logger.error('❌ Error ending streaming session:', error);
    }
  }

  cleanup(): void {
    this.audioBuffer = [];
    this.isProcessingAudio = false;
    
    if (this.ws.readyState === WebSocket.OPEN) {
      this.ws.close(1000, 'Session cleanup');
    }
  }

  // Getters for monitoring
  getState(): SessionState {
    return { ...this.state };
  }

  isActive(): boolean {
    return this.state.status === 'active';
  }
}
