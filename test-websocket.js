const WebSocket = require('ws');

// Test WebSocket connection to our server
const wsUrl = 'wss://tele-ai-production.up.railway.app/exotel/stream';

console.log('🔍 Testing WebSocket connection to:', wsUrl);

const ws = new WebSocket(wsUrl);

ws.on('open', function() {
    console.log('✅ WebSocket connected successfully');
    
    // Send test message
    console.log('📤 Sending test message...');
    ws.send(JSON.stringify({
        event: 'test',
        message: 'Hello from test client'
    }));
    
    // Send mock start message (Exotel format)
    setTimeout(() => {
        console.log('📤 Sending mock start message...');
        const startMessage = {
            event: 'start',
            sequence_number: 1,
            stream_sid: 'test-stream-123',
            start: {
                stream_sid: 'test-stream-123',
                call_sid: 'test-call-456',
                account_sid: 'test-account-789',
                from: '+************',
                to: '+************',
                media_format: {
                    encoding: 'raw',
                    sample_rate: '8000',
                    bit_rate: '16'
                }
            }
        };
        console.log('📤 Start message JSON:', JSON.stringify(startMessage, null, 2));
        ws.send(JSON.stringify(startMessage));
    }, 1000);
    
    // Send mock media message
    setTimeout(() => {
        console.log('📤 Sending mock media message...');
        ws.send(JSON.stringify({
            event: 'media',
            sequence_number: 2,
            stream_sid: 'test-stream-123',
            media: {
                chunk: 1,
                timestamp: '100',
                payload: 'dGVzdCBhdWRpbyBkYXRh' // base64 encoded "test audio data"
            }
        }));
    }, 2000);
    
    // Send mock stop message
    setTimeout(() => {
        console.log('📤 Sending mock stop message...');
        ws.send(JSON.stringify({
            event: 'stop',
            sequence_number: 3,
            stream_sid: 'test-stream-123',
            stop: {
                call_sid: 'test-call-456',
                account_sid: 'test-account-789',
                reason: 'test completed'
            }
        }));
        
        // Close connection after test
        setTimeout(() => {
            console.log('🔌 Closing connection...');
            ws.close();
        }, 1000);
    }, 3000);
});

ws.on('message', function(data) {
    console.log('📨 Received from server:', data.toString());
});

ws.on('error', function(error) {
    console.error('❌ WebSocket error:', error);
});

ws.on('close', function(code, reason) {
    console.log('🔌 WebSocket closed:', code, reason.toString());
});
