import { useState, useCallback, useMemo } from 'react';
import { DNDRecord, DNDResultsState, DNDUIState, DNDValidationResponse } from '../types';

export interface UseDndResultsReturn {
  resultsState: DNDResultsState;
  uiState: DNDUIState;
  filteredData: DNDRecord[];
  paginatedData: DNDRecord[];
  totalPages: number;
  setResults: (response: DNDValidationResponse) => void;
  updateFilters: (filters: Partial<DNDUIState['filters']>) => void;
  updateSort: (sortBy: keyof DNDRecord, sortOrder?: 'asc' | 'desc') => void;
  updatePagination: (page: number, pageSize?: number) => void;
  clearResults: () => void;
}

export const useDndResults = (): UseDndResultsReturn => {
  const [resultsState, setResultsState] = useState<DNDResultsState>({
    data: [],
    summary: null,
    errors: [],
    metadata: null,
    isLoading: false,
  });

  const [uiState, setUiState] = useState<DNDUIState>({
    currentPage: 1,
    pageSize: 50,
    sortBy: 'name',
    sortOrder: 'asc',
    filters: {
      dndStatus: 'all',
      searchTerm: '',
      showErrorsOnly: false,
    },
  });

  // Filter and search data
  const filteredData = useMemo(() => {
    let filtered = [...resultsState.data];

    // Filter by DND status
    if (uiState.filters.dndStatus !== 'all') {
      filtered = filtered.filter(record => record.dnd_status === uiState.filters.dndStatus);
    }

    // Filter by errors only
    if (uiState.filters.showErrorsOnly) {
      filtered = filtered.filter(record => record.dnd_status === 'Error');
    }

    // Search filter
    if (uiState.filters.searchTerm) {
      const searchTerm = uiState.filters.searchTerm.toLowerCase();
      filtered = filtered.filter(record =>
        record.name.toLowerCase().includes(searchTerm) ||
        record.phone.toLowerCase().includes(searchTerm) ||
        record.email.toLowerCase().includes(searchTerm) ||
        (record.error_message && record.error_message.toLowerCase().includes(searchTerm))
      );
    }

    // Sort data
    filtered.sort((a, b) => {
      const aValue = a[uiState.sortBy];
      const bValue = b[uiState.sortBy];
      
      if (aValue < bValue) {
        return uiState.sortOrder === 'asc' ? -1 : 1;
      }
      if (aValue > bValue) {
        return uiState.sortOrder === 'asc' ? 1 : -1;
      }
      return 0;
    });

    return filtered;
  }, [resultsState.data, uiState.filters, uiState.sortBy, uiState.sortOrder]);

  // Paginate data
  const paginatedData = useMemo(() => {
    const startIndex = (uiState.currentPage - 1) * uiState.pageSize;
    const endIndex = startIndex + uiState.pageSize;
    return filteredData.slice(startIndex, endIndex);
  }, [filteredData, uiState.currentPage, uiState.pageSize]);

  // Calculate total pages
  const totalPages = useMemo(() => {
    return Math.ceil(filteredData.length / uiState.pageSize);
  }, [filteredData.length, uiState.pageSize]);

  const setResults = useCallback((response: DNDValidationResponse) => {
    setResultsState({
      data: response.results,
      summary: response.summary,
      errors: response.errors,
      metadata: response.metadata,
      isLoading: false,
    });
    
    // Reset to first page when new results are loaded
    setUiState(prev => ({
      ...prev,
      currentPage: 1,
    }));
  }, []);

  const updateFilters = useCallback((filters: Partial<DNDUIState['filters']>) => {
    setUiState(prev => ({
      ...prev,
      filters: {
        ...prev.filters,
        ...filters,
      },
      currentPage: 1, // Reset to first page when filters change
    }));
  }, []);

  const updateSort = useCallback((sortBy: keyof DNDRecord, sortOrder?: 'asc' | 'desc') => {
    setUiState(prev => ({
      ...prev,
      sortBy,
      sortOrder: sortOrder || (prev.sortBy === sortBy && prev.sortOrder === 'asc' ? 'desc' : 'asc'),
      currentPage: 1, // Reset to first page when sort changes
    }));
  }, []);

  const updatePagination = useCallback((page: number, pageSize?: number) => {
    setUiState(prev => ({
      ...prev,
      currentPage: page,
      ...(pageSize && { pageSize }),
    }));
  }, []);

  const clearResults = useCallback(() => {
    setResultsState({
      data: [],
      summary: null,
      errors: [],
      metadata: null,
      isLoading: false,
    });
    setUiState(prev => ({
      ...prev,
      currentPage: 1,
      filters: {
        dndStatus: 'all',
        searchTerm: '',
        showErrorsOnly: false,
      },
    }));
  }, []);

  return {
    resultsState,
    uiState,
    filteredData,
    paginatedData,
    totalPages,
    setResults,
    updateFilters,
    updateSort,
    updatePagination,
    clearResults,
  };
};
