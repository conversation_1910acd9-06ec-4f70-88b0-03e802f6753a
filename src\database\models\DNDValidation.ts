import { db } from '../connection';
import { DNDValidation, CreateDNDValidationRequest, UpdateDNDValidationRequest, DNDValidationStats } from '../../types';
import logger from '../../utils/logger';

export class DNDValidationModel {
  /**
   * Find DND validation by phone number
   */
  static async findByPhone(phoneNumber: string): Promise<DNDValidation | null> {
    try {
      const result = await db.query(`
        SELECT phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
        FROM dnd_validations 
        WHERE phone_number = $1
      `, [phoneNumber]);
      
      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return {
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    } catch (error) {
      logger.error('Error finding DND validation by phone:', error);
      throw error;
    }
  }

  /**
   * Find all DND validations with pagination and filtering
   */
  static async findAll(options: {
    page?: number;
    limit?: number;
    dndStatus?: 'DND' | 'Non-DND' | 'Error';
    search?: string;
  } = {}): Promise<{ validations: DNDValidation[]; total: number }> {
    const { page = 1, limit = 10, dndStatus, search } = options;
    const offset = (page - 1) * limit;

    let whereClause = '';
    const queryParams: any[] = [];
    let paramCount = 1;

    const conditions: string[] = [];

    if (dndStatus) {
      conditions.push(`dnd_status = $${paramCount++}`);
      queryParams.push(dndStatus);
    }

    if (search) {
      conditions.push(`phone_number ILIKE $${paramCount}`);
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    try {
      // Get total count
      const countResult = await db.query(`
        SELECT COUNT(*) as count 
        FROM dnd_validations 
        ${whereClause}
      `, queryParams);

      const total = parseInt(countResult.rows[0].count);

      // Get validations with pagination
      queryParams.push(limit, offset);
      const result = await db.query(`
        SELECT phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
        FROM dnd_validations 
        ${whereClause}
        ORDER BY validated_at DESC
        LIMIT $${paramCount++} OFFSET $${paramCount++}
      `, queryParams);

      const validations = result.rows.map((row: any) => ({
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));

      return { validations, total };
    } catch (error) {
      logger.error('Error finding DND validations:', error);
      throw error;
    }
  }

  /**
   * Create or update DND validation (upsert)
   */
  static async upsert(validationData: CreateDNDValidationRequest): Promise<DNDValidation> {
    const { phoneNumber, dndStatus, validatedAt = new Date(), validationMetadata = {} } = validationData;

    try {
      const result = await db.query(`
        INSERT INTO dnd_validations (phone_number, dnd_status, validated_at, validation_metadata)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (phone_number) 
        DO UPDATE SET 
          dnd_status = EXCLUDED.dnd_status,
          validated_at = EXCLUDED.validated_at,
          validation_metadata = EXCLUDED.validation_metadata,
          updated_at = CURRENT_TIMESTAMP
        RETURNING phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
      `, [phoneNumber, dndStatus, validatedAt, JSON.stringify(validationMetadata)]);

      const row = result.rows[0];
      return {
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    } catch (error) {
      logger.error('Error upserting DND validation:', error);
      throw error;
    }
  }

  /**
   * Bulk upsert DND validations
   */
  static async bulkUpsert(validations: CreateDNDValidationRequest[]): Promise<DNDValidation[]> {
    if (validations.length === 0) {
      return [];
    }

    try {
      // Build the VALUES clause for bulk insert
      const values: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      validations.forEach((validation) => {
        const { phoneNumber, dndStatus, validatedAt = new Date(), validationMetadata = {} } = validation;
        values.push(`($${paramIndex++}, $${paramIndex++}, $${paramIndex++}, $${paramIndex++})`);
        params.push(phoneNumber, dndStatus, validatedAt, JSON.stringify(validationMetadata));
      });

      const result = await db.query(`
        INSERT INTO dnd_validations (phone_number, dnd_status, validated_at, validation_metadata)
        VALUES ${values.join(', ')}
        ON CONFLICT (phone_number) 
        DO UPDATE SET 
          dnd_status = EXCLUDED.dnd_status,
          validated_at = EXCLUDED.validated_at,
          validation_metadata = EXCLUDED.validation_metadata,
          updated_at = CURRENT_TIMESTAMP
        RETURNING phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
      `, params);

      return result.rows.map((row: any) => ({
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } catch (error) {
      logger.error('Error bulk upserting DND validations:', error);
      throw error;
    }
  }

  /**
   * Update existing DND validation
   */
  static async update(phoneNumber: string, updateData: UpdateDNDValidationRequest): Promise<DNDValidation> {
    const validation = await this.findByPhone(phoneNumber);
    if (!validation) {
      throw new Error('DND validation not found');
    }

    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramCount = 1;

    if (updateData.dndStatus !== undefined) {
      updateFields.push(`dnd_status = $${paramCount++}`);
      updateValues.push(updateData.dndStatus);
    }

    if (updateData.validatedAt !== undefined) {
      updateFields.push(`validated_at = $${paramCount++}`);
      updateValues.push(updateData.validatedAt);
    }

    if (updateData.validationMetadata !== undefined) {
      updateFields.push(`validation_metadata = $${paramCount++}`);
      updateValues.push(JSON.stringify(updateData.validationMetadata));
    }

    if (updateFields.length === 0) {
      return validation;
    }

    updateValues.push(phoneNumber);

    try {
      const result = await db.query(`
        UPDATE dnd_validations 
        SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE phone_number = $${paramCount}
        RETURNING phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
      `, updateValues);

      const row = result.rows[0];
      return {
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    } catch (error) {
      logger.error('Error updating DND validation:', error);
      throw error;
    }
  }

  /**
   * Delete DND validation by phone number
   */
  static async delete(phoneNumber: string): Promise<void> {
    try {
      const result = await db.query('DELETE FROM dnd_validations WHERE phone_number = $1', [phoneNumber]);

      if (result.rowCount === 0) {
        throw new Error('DND validation not found');
      }
    } catch (error) {
      logger.error('Error deleting DND validation:', error);
      throw error;
    }
  }

  /**
   * Get DND validation statistics
   */
  static async getStats(): Promise<DNDValidationStats> {
    try {
      const totalResult = await db.query('SELECT COUNT(*) as count FROM dnd_validations');
      const statusResult = await db.query(`
        SELECT dnd_status, COUNT(*) as count 
        FROM dnd_validations 
        GROUP BY dnd_status
      `);
      const lastValidatedResult = await db.query(`
        SELECT MAX(validated_at) as last_validated_at 
        FROM dnd_validations
      `);

      const statusCounts: Record<string, number> = {};
      statusResult.rows.forEach((row: any) => {
        statusCounts[row.dnd_status] = parseInt(row.count);
      });

      return {
        totalValidations: parseInt(totalResult.rows[0].count),
        dndCount: statusCounts['DND'] || 0,
        nonDndCount: statusCounts['Non-DND'] || 0,
        errorCount: statusCounts['Error'] || 0,
        lastValidatedAt: lastValidatedResult.rows[0].last_validated_at || undefined
      };
    } catch (error) {
      logger.error('Error getting DND validation stats:', error);
      throw error;
    }
  }

  /**
   * Check if phone number has been validated recently (within specified hours)
   */
  static async isRecentlyValidated(phoneNumber: string, withinHours: number = 24): Promise<boolean> {
    try {
      const result = await db.query(`
        SELECT 1 FROM dnd_validations 
        WHERE phone_number = $1 
        AND validated_at > NOW() - INTERVAL '${withinHours} hours'
      `, [phoneNumber]);

      return result.rows.length > 0;
    } catch (error) {
      logger.error('Error checking recent validation:', error);
      throw error;
    }
  }
}

export default DNDValidationModel;
