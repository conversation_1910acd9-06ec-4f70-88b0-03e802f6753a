/**
 * @fileoverview DND Validation Database Model
 * @description Comprehensive database model for DND validation operations with enhanced error handling
 * <AUTHOR> System
 * @version 2.0.0
 */

import { db } from '../connection';
import {
  DNDValidation,
  CreateDNDValidationRequest,
  UpdateDNDValidationRequest,
  DNDValidationStats,
  DNDValidationQueryOptions,
  DNDValidationQueryResult,
  DNDHealthCheckResult,
  IDNDValidationRepository
} from '../../types';
import { DND_PERSISTENCE_QUERIES } from '../../config/dndPersistence';
import {
  DNDErrorFactory,
  ValidationNotFoundError
} from '../../utils/dndPersistenceErrors';
import { dndValidator, validateAndThrow } from '../../utils/dndValidator';
import logger from '../../utils/logger';

/**
 * Enhanced DND Validation Model with comprehensive error handling and validation
 * Provides both instance methods (implementing IDNDValidationRepository) and static methods for backward compatibility
 */
export class DNDValidationModel implements IDNDValidationRepository {
  /**
   * Finds a DND validation record by phone number
   * @param {string} phoneNumber - The phone number to search for
   * @returns {Promise<DNDValidation | null>} The validation record or null if not found
   * @throws {InvalidPhoneNumberError} When phone number format is invalid
   * @throws {DatabaseConnectionError} When database connection fails
   * @throws {QueryTimeoutError} When query times out
   *
   * @example
   * ```typescript
   * const model = new DNDValidationModel();
   * const validation = await model.findByPhone('9876543210');
   * if (validation) {
   *   console.log(`DND Status: ${validation.dndStatus}`);
   * }
   * ```
   */
  async findByPhone(phoneNumber: string): Promise<DNDValidation | null> {
    const startTime = Date.now();

    try {
      // Validate and sanitize phone number
      const phoneValidation = dndValidator.validatePhoneNumber(phoneNumber);
      validateAndThrow(phoneValidation, 'phoneNumber');

      const sanitizedPhone = phoneValidation.sanitized!;

      logger.debug('Finding DND validation by phone', { phoneNumber: sanitizedPhone });

      const result = await db.query(DND_PERSISTENCE_QUERIES.FIND_BY_PHONE, [sanitizedPhone]);

      const queryTime = Date.now() - startTime;
      logger.debug('DND validation query completed', {
        phoneNumber: sanitizedPhone,
        found: result.rows.length > 0,
        queryTimeMs: queryTime
      });

      if (result.rows.length === 0) {
        return null;
      }

      const row = result.rows[0];
      return DNDValidationModel.mapRowToValidation(row);

    } catch (error) {
      const queryTime = Date.now() - startTime;
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      logger.error('Error finding DND validation by phone', {
        phoneNumber,
        queryTimeMs: queryTime,
        error: errorMessage
      });

      throw DNDErrorFactory.fromDatabaseError(
        error instanceof Error ? error : new Error(String(error)),
        'findByPhone',
        { phoneNumber }
      );
    }
  }

  /**
   * Maps database row to DNDValidation object
   * @private
   * @param {any} row - Database row
   * @returns {DNDValidation} Mapped validation object
   */
  private static mapRowToValidation(row: any): DNDValidation {
    return {
      phoneNumber: row.phone_number,
      dndStatus: row.dnd_status,
      validatedAt: new Date(row.validated_at),
      validationMetadata: row.validation_metadata || {},
      createdAt: new Date(row.created_at),
      updatedAt: new Date(row.updated_at)
    };
  }

  /**
   * Finds all DND validations with pagination and filtering
   * @param {DNDValidationQueryOptions} options - Query options for filtering and pagination
   * @returns {Promise<DNDValidationQueryResult>} Paginated validation results
   * @throws {DatabaseConnectionError} When database connection fails
   * @throws {QueryTimeoutError} When query times out
   *
   * @example
   * ```typescript
   * const model = new DNDValidationModel();
   * const result = await model.findAll({
   *   page: 1,
   *   limit: 10,
   *   dndStatus: 'DND',
   *   search: '9876'
   * });
   * ```
   */
  async findAll(options: DNDValidationQueryOptions = {}): Promise<DNDValidationQueryResult> {
    const { page = 1, limit = 10, dndStatus, search } = options;
    const offset = (page - 1) * limit;

    let whereClause = '';
    const queryParams: any[] = [];
    let paramCount = 1;

    const conditions: string[] = [];

    if (dndStatus) {
      conditions.push(`dnd_status = $${paramCount++}`);
      queryParams.push(dndStatus);
    }

    if (search) {
      conditions.push(`phone_number ILIKE $${paramCount}`);
      queryParams.push(`%${search}%`);
      paramCount++;
    }

    if (conditions.length > 0) {
      whereClause = `WHERE ${conditions.join(' AND ')}`;
    }

    try {
      // Get total count
      const countResult = await db.query(`
        SELECT COUNT(*) as count 
        FROM dnd_validations 
        ${whereClause}
      `, queryParams);

      const total = parseInt(countResult.rows[0].count);

      // Get validations with pagination
      queryParams.push(limit, offset);
      const result = await db.query(`
        SELECT phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
        FROM dnd_validations 
        ${whereClause}
        ORDER BY validated_at DESC
        LIMIT $${paramCount++} OFFSET $${paramCount++}
      `, queryParams);

      const validations = result.rows.map((row: any) => ({
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));

      const totalPages = Math.ceil(total / limit);

      return {
        validations,
        total,
        page,
        limit,
        totalPages
      };
    } catch (error) {
      logger.error('Error finding DND validations:', error);
      throw error;
    }
  }

  /**
   * Creates or updates a DND validation record (upsert operation)
   * @param {CreateDNDValidationRequest} validationData - Validation data to upsert
   * @returns {Promise<DNDValidation>} The created or updated validation record
   * @throws {InvalidPhoneNumberError} When phone number format is invalid
   * @throws {InvalidDNDStatusError} When DND status is invalid
   * @throws {DatabaseConnectionError} When database connection fails
   *
   * @example
   * ```typescript
   * const model = new DNDValidationModel();
   * const validation = await model.upsert({
   *   phoneNumber: '9876543210',
   *   dndStatus: 'DND',
   *   validationMetadata: { source: 'api' }
   * });
   * ```
   */
  async upsert(validationData: CreateDNDValidationRequest): Promise<DNDValidation> {
    return DNDValidationModel.upsertStatic(validationData);
  }

  /**
   * Static version of upsert for backward compatibility
   * @param {CreateDNDValidationRequest} validationData - Validation data to upsert
   * @returns {Promise<DNDValidation>} The created or updated validation record
   */
  static async upsertStatic(validationData: CreateDNDValidationRequest): Promise<DNDValidation> {
    const { phoneNumber, dndStatus, validatedAt = new Date(), validationMetadata = {} } = validationData;

    try {
      const result = await db.query(`
        INSERT INTO dnd_validations (phone_number, dnd_status, validated_at, validation_metadata)
        VALUES ($1, $2, $3, $4)
        ON CONFLICT (phone_number) 
        DO UPDATE SET 
          dnd_status = EXCLUDED.dnd_status,
          validated_at = EXCLUDED.validated_at,
          validation_metadata = EXCLUDED.validation_metadata,
          updated_at = CURRENT_TIMESTAMP
        RETURNING phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
      `, [phoneNumber, dndStatus, validatedAt, JSON.stringify(validationMetadata)]);

      const row = result.rows[0];
      return {
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    } catch (error) {
      logger.error('Error upserting DND validation:', error);
      throw error;
    }
  }

  /**
   * Bulk upserts multiple DND validation records
   * @param {CreateDNDValidationRequest[]} validations - Array of validation data to upsert
   * @returns {Promise<DNDValidation[]>} Array of created or updated validation records
   */
  async bulkUpsert(validations: CreateDNDValidationRequest[]): Promise<DNDValidation[]> {
    return DNDValidationModel.bulkUpsertStatic(validations);
  }

  /**
   * Updates an existing DND validation record
   * @param {string} phoneNumber - Phone number to update
   * @param {UpdateDNDValidationRequest} updateData - Data to update
   * @returns {Promise<DNDValidation>} Updated validation record
   */
  async update(phoneNumber: string, updateData: UpdateDNDValidationRequest): Promise<DNDValidation> {
    return DNDValidationModel.updateStatic(phoneNumber, updateData);
  }

  /**
   * Deletes a DND validation record
   * @param {string} phoneNumber - Phone number to delete
   * @returns {Promise<void>}
   */
  async delete(phoneNumber: string): Promise<void> {
    return DNDValidationModel.deleteStatic(phoneNumber);
  }

  /**
   * Gets DND validation statistics
   * @returns {Promise<DNDValidationStats>} Validation statistics
   */
  async getStats(): Promise<DNDValidationStats> {
    return DNDValidationModel.getStatsStatic();
  }

  /**
   * Checks if a phone number has been validated recently
   * @param {string} phoneNumber - Phone number to check
   * @param {number} withinHours - Hours to check within
   * @returns {Promise<boolean>} True if recently validated
   */
  async isRecentlyValidated(phoneNumber: string, withinHours: number = 24): Promise<boolean> {
    return DNDValidationModel.isRecentlyValidatedStatic(phoneNumber, withinHours);
  }

  /**
   * Performs health check on the DND validation system
   * @returns {Promise<DNDHealthCheckResult>} Health check results
   */
  async healthCheck(): Promise<DNDHealthCheckResult> {
    return DNDValidationModel.healthCheckStatic();
  }

  /**
   * Static version of bulk upsert for backward compatibility
   */
  static async bulkUpsertStatic(validations: CreateDNDValidationRequest[]): Promise<DNDValidation[]> {
    if (validations.length === 0) {
      return [];
    }

    try {
      // Build the VALUES clause for bulk insert
      const values: string[] = [];
      const params: any[] = [];
      let paramIndex = 1;

      validations.forEach((validation) => {
        const { phoneNumber, dndStatus, validatedAt = new Date(), validationMetadata = {} } = validation;
        values.push(`($${paramIndex++}, $${paramIndex++}, $${paramIndex++}, $${paramIndex++})`);
        params.push(phoneNumber, dndStatus, validatedAt, JSON.stringify(validationMetadata));
      });

      const result = await db.query(`
        INSERT INTO dnd_validations (phone_number, dnd_status, validated_at, validation_metadata)
        VALUES ${values.join(', ')}
        ON CONFLICT (phone_number) 
        DO UPDATE SET 
          dnd_status = EXCLUDED.dnd_status,
          validated_at = EXCLUDED.validated_at,
          validation_metadata = EXCLUDED.validation_metadata,
          updated_at = CURRENT_TIMESTAMP
        RETURNING phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
      `, params);

      return result.rows.map((row: any) => ({
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      }));
    } catch (error) {
      logger.error('Error bulk upserting DND validations:', error);
      throw error;
    }
  }

  /**
   * Update existing DND validation
   */
  static async updateStatic(phoneNumber: string, updateData: UpdateDNDValidationRequest): Promise<DNDValidation> {
    const model = new DNDValidationModel();
    const validation = await model.findByPhone(phoneNumber);
    if (!validation) {
      throw new Error('DND validation not found');
    }

    const updateFields: string[] = [];
    const updateValues: any[] = [];
    let paramCount = 1;

    if (updateData.dndStatus !== undefined) {
      updateFields.push(`dnd_status = $${paramCount++}`);
      updateValues.push(updateData.dndStatus);
    }

    if (updateData.validatedAt !== undefined) {
      updateFields.push(`validated_at = $${paramCount++}`);
      updateValues.push(updateData.validatedAt);
    }

    if (updateData.validationMetadata !== undefined) {
      updateFields.push(`validation_metadata = $${paramCount++}`);
      updateValues.push(JSON.stringify(updateData.validationMetadata));
    }

    if (updateFields.length === 0) {
      return validation;
    }

    updateValues.push(phoneNumber);

    try {
      const result = await db.query(`
        UPDATE dnd_validations 
        SET ${updateFields.join(', ')}, updated_at = CURRENT_TIMESTAMP
        WHERE phone_number = $${paramCount}
        RETURNING phone_number, dnd_status, validated_at, validation_metadata, created_at, updated_at
      `, updateValues);

      const row = result.rows[0];
      return {
        phoneNumber: row.phone_number,
        dndStatus: row.dnd_status,
        validatedAt: row.validated_at,
        validationMetadata: row.validation_metadata || {},
        createdAt: row.created_at,
        updatedAt: row.updated_at
      };
    } catch (error) {
      logger.error('Error updating DND validation:', error);
      throw error;
    }
  }

  /**
   * Delete DND validation by phone number
   */
  static async deleteStatic(phoneNumber: string): Promise<void> {
    try {
      const result = await db.query('DELETE FROM dnd_validations WHERE phone_number = $1', [phoneNumber]);

      if (result.rowCount === 0) {
        throw new Error('DND validation not found');
      }
    } catch (error) {
      logger.error('Error deleting DND validation:', error);
      throw error;
    }
  }

  /**
   * Get DND validation statistics
   */
  static async getStatsStatic(): Promise<DNDValidationStats> {
    try {
      const totalResult = await db.query('SELECT COUNT(*) as count FROM dnd_validations');
      const statusResult = await db.query(`
        SELECT dnd_status, COUNT(*) as count 
        FROM dnd_validations 
        GROUP BY dnd_status
      `);
      const lastValidatedResult = await db.query(`
        SELECT MAX(validated_at) as last_validated_at 
        FROM dnd_validations
      `);

      const statusCounts: Record<string, number> = {};
      statusResult.rows.forEach((row: any) => {
        statusCounts[row.dnd_status] = parseInt(row.count);
      });

      return {
        totalValidations: parseInt(totalResult.rows[0].count),
        dndCount: statusCounts['DND'] || 0,
        nonDndCount: statusCounts['Non-DND'] || 0,
        errorCount: statusCounts['Error'] || 0,
        lastValidatedAt: lastValidatedResult.rows[0].last_validated_at || undefined
      };
    } catch (error) {
      logger.error('Error getting DND validation stats:', error);
      throw error;
    }
  }

  /**
   * Check if phone number has been validated recently (within specified hours)
   */
  static async isRecentlyValidatedStatic(phoneNumber: string, withinHours: number = 24): Promise<boolean> {
    try {
      const result = await db.query(`
        SELECT 1 FROM dnd_validations 
        WHERE phone_number = $1 
        AND validated_at > NOW() - INTERVAL '${withinHours} hours'
      `, [phoneNumber]);

      return result.rows.length > 0;
    } catch (error) {
      logger.error('Error checking recent validation:', error);
      throw error;
    }
  }

  /**
   * Performs health check on the DND validation system
   * @returns {Promise<DNDHealthCheckResult>} Health check results
   */
  static async healthCheckStatic(): Promise<DNDHealthCheckResult> {
    const startTime = Date.now();

    try {
      const result = await db.query(DND_PERSISTENCE_QUERIES.HEALTH_CHECK);
      const row = result.rows[0];
      const responseTime = Date.now() - startTime;

      const totalRecords = parseInt(row.total_records || '0');
      const recentRecords = parseInt(row.recent_records || '0');
      const databaseSize = parseInt(row.database_size || '0');

      let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
      const errors: string[] = [];

      // Check response time
      if (responseTime > 5000) {
        status = 'unhealthy';
        errors.push(`Slow response time: ${responseTime}ms`);
      } else if (responseTime > 1000) {
        status = 'degraded';
        errors.push(`Degraded response time: ${responseTime}ms`);
      }

      // Check database size (warn if > 1GB)
      if (databaseSize > 1024 * 1024 * 1024) {
        if (status === 'healthy') status = 'degraded';
        errors.push(`Large database size: ${Math.round(databaseSize / 1024 / 1024)}MB`);
      }

      const healthResult: DNDHealthCheckResult = {
        status,
        totalRecords,
        recentRecords,
        databaseSize,
        responseTime
      };

      if (errors.length > 0) {
        healthResult.errors = errors;
      }

      return healthResult;
    } catch (error) {
      const responseTime = Date.now() - startTime;
      logger.error('Health check failed:', error);

      return {
        status: 'unhealthy',
        totalRecords: 0,
        recentRecords: 0,
        databaseSize: 0,
        responseTime,
        errors: ['Database connection failed']
      };
    }
  }
}

export default DNDValidationModel;
