import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '../components/ui/Card';
import { DndUploadForm } from '../components/dnd/DndUploadForm';
import { DndResultsManager } from '../components/dnd/DndResultsManager';
import { useDndUpload } from '../hooks/useDndUpload';
import { useDndResults } from '../hooks/useDndResults';
import { DNDValidationResponse } from '../types';

export const DndValidationPage: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<'upload' | 'results'>('upload');
  const { uploadState, uploadFile, resetUpload, validateFile, setFile } = useDndUpload();
  const { 
    resultsState, 
    uiState, 
    filteredData, 
    paginatedData, 
    totalPages,
    setResults, 
    updateFilters, 
    updateSort, 
    updatePagination, 
    clearResults 
  } = useDndResults();

  const handleUploadSuccess = (response: DNDValidationResponse) => {
    setResults(response);
    setCurrentStep('results');
  };

  const handleStartOver = () => {
    resetUpload();
    clearResults();
    setCurrentStep('upload');
  };

  const handleBackToUpload = () => {
    setCurrentStep('upload');
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">DND Validation</h1>
          <p className="mt-2 text-gray-600">
            Upload CSV files to validate phone numbers against Do Not Disturb (DND) registries
          </p>
        </div>

        {/* Progress Steps */}
        <div className="mb-8">
          <nav aria-label="Progress">
            <ol className="flex items-center">
              <li className="relative">
                <div className={`flex items-center ${
                  currentStep === 'upload' ? 'text-primary-600' : 'text-gray-500'
                }`}>
                  <div className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                    currentStep === 'upload' 
                      ? 'border-primary-600 bg-primary-600 text-white' 
                      : resultsState.data.length > 0
                        ? 'border-green-600 bg-green-600 text-white'
                        : 'border-gray-300 bg-white text-gray-500'
                  }`}>
                    {resultsState.data.length > 0 && currentStep === 'results' ? (
                      <svg className="h-5 w-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    ) : (
                      <span className="text-sm font-medium">1</span>
                    )}
                  </div>
                  <span className="ml-3 text-sm font-medium">Upload CSV</span>
                </div>
              </li>

              <li className="relative ml-8">
                <div className="flex items-center pl-12">
                  <div className="absolute -left-4 h-0.5 w-8 bg-gray-300" />
                  <div className={`flex h-8 w-8 items-center justify-center rounded-full border-2 ${
                    currentStep === 'results' && resultsState.data.length > 0
                      ? 'border-primary-600 bg-primary-600 text-white'
                      : 'border-gray-300 bg-white text-gray-500'
                  }`}>
                    <span className="text-sm font-medium">2</span>
                  </div>
                  <span className={`ml-3 text-sm font-medium ${
                    currentStep === 'results' && resultsState.data.length > 0
                      ? 'text-primary-600'
                      : 'text-gray-500'
                  }`}>
                    View Results
                  </span>
                </div>
              </li>
            </ol>
          </nav>
        </div>

        {/* Main Content */}
        {currentStep === 'upload' ? (
          <Card>
            <CardHeader>
              <CardTitle>Upload CSV File</CardTitle>
            </CardHeader>
            <CardContent>
              <DndUploadForm
                uploadState={uploadState}
                onUpload={uploadFile}
                onValidate={validateFile}
                onSuccess={handleUploadSuccess}
                onReset={resetUpload}
                onSetFile={setFile}
              />
            </CardContent>
          </Card>
        ) : (
          <DndResultsManager
            resultsState={resultsState}
            uiState={uiState}
            filteredData={filteredData}
            paginatedData={paginatedData}
            totalPages={totalPages}
            onUpdateFilters={updateFilters}
            onUpdateSort={updateSort}
            onUpdatePagination={updatePagination}
            onStartOver={handleStartOver}
            onBackToUpload={handleBackToUpload}
          />
        )}
      </div>
    </div>
  );
};
