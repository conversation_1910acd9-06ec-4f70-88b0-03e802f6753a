import { db } from '@/database/connection';
import DNDPersistenceService from '@/services/dndPersistenceService';
import { DNDCheckResult, BulkDNDResponse } from '@/services/dndService';

describe('DNDPersistenceService', () => {
  let persistenceService: DNDPersistenceService;

  beforeAll(async () => {
    await db.connect();
    persistenceService = new DNDPersistenceService();
  });

  afterAll(async () => {
    // Clean up test data
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['test%']);
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['9876%']);
    await db.disconnect();
  });

  beforeEach(async () => {
    // Clear any existing test data
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['test%']);
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['9876%']);
  });

  describe('saveDNDValidation', () => {
    it('should save a single DND validation result', async () => {
      const result: DNDCheckResult = {
        phone: '+919876543210',
        dnd_status: 'DND',
        name: 'Test User',
        email: '<EMAIL>'
      };

      const saved = await persistenceService.saveDNDValidation(result);

      expect(saved.phoneNumber).toBe('9876543210'); // Normalized
      expect(saved.dndStatus).toBe('DND');
      expect(saved.validationMetadata?.name).toBe('Test User');
      expect(saved.validationMetadata?.email).toBe('<EMAIL>');
      expect(saved.validationMetadata?.originalPhone).toBe('+919876543210');
      expect(saved.validationMetadata?.normalizedPhone).toBe('9876543210');
    });

    it('should save validation with error status and error message', async () => {
      const result: DNDCheckResult = {
        phone: 'invalid-phone',
        dnd_status: 'Error',
        error_message: 'Invalid phone number format'
      };

      const saved = await persistenceService.saveDNDValidation(result);

      expect(saved.dndStatus).toBe('Error');
      expect(saved.validationMetadata?.errorMessage).toBe('Invalid phone number format');
      expect(saved.validationMetadata?.isValidFormat).toBe(false);
    });

    it('should skip recent validations when option is enabled', async () => {
      const result: DNDCheckResult = {
        phone: '+919876543211',
        dnd_status: 'DND'
      };

      // Save initial validation
      const initial = await persistenceService.saveDNDValidation(result);

      // Try to save again with skipRecentValidations option
      const result2: DNDCheckResult = {
        phone: '+919876543211',
        dnd_status: 'Non-DND'
      };

      const skipped = await persistenceService.saveDNDValidation(result2, {
        skipRecentValidations: true,
        recentValidationHours: 24
      });

      // Should return the original validation, not update it
      expect(skipped.dndStatus).toBe('DND');
      expect(skipped.createdAt).toEqual(initial.createdAt);
    });

    it('should save without metadata when includeMetadata is false', async () => {
      const result: DNDCheckResult = {
        phone: '+919876543212',
        dnd_status: 'Non-DND',
        name: 'Test User',
        email: '<EMAIL>'
      };

      const saved = await persistenceService.saveDNDValidation(result, {
        includeMetadata: false
      });

      expect(saved.validationMetadata).toEqual({});
    });
  });

  describe('saveBulkDNDValidations', () => {
    it('should save multiple DND validation results', async () => {
      const results: DNDCheckResult[] = [
        { phone: '+919876543213', dnd_status: 'DND', name: 'User 1' },
        { phone: '+919876543214', dnd_status: 'Non-DND', name: 'User 2' },
        { phone: '+919876543215', dnd_status: 'Error', error_message: 'API Error' }
      ];

      const saved = await persistenceService.saveBulkDNDValidations(results);

      expect(saved).toHaveLength(3);
      expect(saved.map(s => s.dndStatus)).toEqual(['DND', 'Non-DND', 'Error']);
      expect(saved[0]?.validationMetadata?.name).toBe('User 1');
      expect(saved[2]?.validationMetadata?.errorMessage).toBe('API Error');
    });

    it('should handle empty results array', async () => {
      const saved = await persistenceService.saveBulkDNDValidations([]);
      expect(saved).toHaveLength(0);
    });

    it('should skip recent validations in bulk operation', async () => {
      const phoneNumber = '+919876543216';
      
      // Save initial validation
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'DND'
      });

      // Try bulk save with same phone number
      const results: DNDCheckResult[] = [
        { phone: phoneNumber, dnd_status: 'Non-DND' },
        { phone: '+919876543217', dnd_status: 'DND' }
      ];

      const saved = await persistenceService.saveBulkDNDValidations(results, {
        skipRecentValidations: true,
        recentValidationHours: 24
      });

      // Should only save the new phone number
      expect(saved).toHaveLength(1);
      expect(saved[0]?.phoneNumber).toBe('9876543217');
    });
  });

  describe('saveBulkDNDResponse', () => {
    it('should save results from bulk DND response', async () => {
      const bulkResponse: BulkDNDResponse = {
        success: true,
        results: [
          { phone: '+919876543218', dnd_status: 'DND' },
          { phone: '+919876543219', dnd_status: 'Non-DND' }
        ],
        summary: {
          totalRecords: 2,
          processedRecords: 2,
          dndCount: 1,
          nonDndCount: 1,
          errorCount: 0
        }
      };

      const saved = await persistenceService.saveBulkDNDResponse(bulkResponse);

      expect(saved).toHaveLength(2);
      expect(saved.map(s => s.dndStatus)).toEqual(['DND', 'Non-DND']);
    });
  });

  describe('getDNDValidation', () => {
    it('should retrieve DND validation by phone number', async () => {
      const phoneNumber = '+919876543220';
      
      // Save validation
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'DND'
      });

      // Retrieve validation
      const retrieved = await persistenceService.getDNDValidation(phoneNumber);

      expect(retrieved).not.toBeNull();
      expect(retrieved!.phoneNumber).toBe('9876543220'); // Normalized
      expect(retrieved!.dndStatus).toBe('DND');
    });

    it('should return null for non-existent phone number', async () => {
      const retrieved = await persistenceService.getDNDValidation('+919999999999');
      expect(retrieved).toBeNull();
    });

    it('should handle phone number normalization in lookup', async () => {
      const normalizedPhone = '9876543221';
      
      // Save with normalized phone
      await persistenceService.saveDNDValidation({
        phone: normalizedPhone,
        dnd_status: 'Non-DND'
      });

      // Retrieve with different format
      const retrieved = await persistenceService.getDNDValidation('+91' + normalizedPhone);

      expect(retrieved).not.toBeNull();
      expect(retrieved!.phoneNumber).toBe(normalizedPhone);
    });
  });

  describe('isRecentlyValidated', () => {
    it('should return true for recently validated phone number', async () => {
      const phoneNumber = '+919876543222';
      
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'DND'
      });

      const isRecent = await persistenceService.isRecentlyValidated(phoneNumber, 24);
      expect(isRecent).toBe(true);
    });

    it('should return false for non-existent phone number', async () => {
      const isRecent = await persistenceService.isRecentlyValidated('+919999999998', 24);
      expect(isRecent).toBe(false);
    });
  });

  describe('getValidationStats', () => {
    beforeEach(async () => {
      // Create test data
      const results: DNDCheckResult[] = [
        { phone: 'test1111111111', dnd_status: 'DND' },
        { phone: 'test2222222222', dnd_status: 'DND' },
        { phone: 'test3333333333', dnd_status: 'Non-DND' },
        { phone: 'test4444444444', dnd_status: 'Error' }
      ];

      await persistenceService.saveBulkDNDValidations(results);
    });

    it('should return correct validation statistics', async () => {
      const stats = await persistenceService.getValidationStats();

      expect(stats.totalValidations).toBe(4);
      expect(stats.dndCount).toBe(2);
      expect(stats.nonDndCount).toBe(1);
      expect(stats.errorCount).toBe(1);
      expect(stats.lastValidatedAt).toBeDefined();
    });
  });

  describe('getDNDValidations', () => {
    beforeEach(async () => {
      // Create test data
      const results: DNDCheckResult[] = [
        { phone: 'test5555555555', dnd_status: 'DND', name: 'User A' },
        { phone: 'test6666666666', dnd_status: 'Non-DND', name: 'User B' },
        { phone: 'test7777777777', dnd_status: 'Error', name: 'User C' }
      ];

      await persistenceService.saveBulkDNDValidations(results);
    });

    it('should return paginated validations', async () => {
      const result = await persistenceService.getDNDValidations({
        page: 1,
        limit: 2
      });

      expect(result.validations).toHaveLength(2);
      expect(result.total).toBe(3);
    });

    it('should filter by DND status', async () => {
      const result = await persistenceService.getDNDValidations({
        dndStatus: 'DND'
      });

      expect(result.validations).toHaveLength(1);
      expect(result.validations[0]?.dndStatus).toBe('DND');
    });

    it('should search by phone number', async () => {
      const result = await persistenceService.getDNDValidations({
        search: '5555'
      });

      expect(result.validations).toHaveLength(1);
      expect(result.validations[0]?.phoneNumber).toBe('test5555555555');
    });
  });

  describe('deleteDNDValidation', () => {
    it('should delete DND validation by phone number', async () => {
      const phoneNumber = '+919876543223';
      
      // Save validation
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'DND'
      });

      // Delete validation
      await persistenceService.deleteDNDValidation(phoneNumber);

      // Verify deletion
      const retrieved = await persistenceService.getDNDValidation(phoneNumber);
      expect(retrieved).toBeNull();
    });

    it('should handle phone number normalization in deletion', async () => {
      const normalizedPhone = '9876543224';
      
      // Save validation
      await persistenceService.saveDNDValidation({
        phone: normalizedPhone,
        dnd_status: 'DND'
      });

      // Delete with different format
      await persistenceService.deleteDNDValidation('+91' + normalizedPhone);

      // Verify deletion
      const retrieved = await persistenceService.getDNDValidation(normalizedPhone);
      expect(retrieved).toBeNull();
    });
  });

  describe('cleanupOldValidations', () => {
    it('should clean up old validations', async () => {
      // This test would require mocking dates or using a test database
      // For now, just test that the method exists and doesn't throw
      const deletedCount = await persistenceService.cleanupOldValidations(90);
      expect(typeof deletedCount).toBe('number');
      expect(deletedCount).toBeGreaterThanOrEqual(0);
    });
  });
});
