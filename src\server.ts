import express from 'express';
import cors from 'cors';
import path from 'path';
import { createServer } from 'http';
import leadsRouter from './routes/leads';
import callsRouter from './routes/calls';
import webhooksRouter from './routes/webhooks';
import conversationRouter from './routes/conversation';
import streamingRouter from './routes/streaming';
import dndRouter from './routes/dnd';
import webCallRouter from './routes/web-call';
import { db } from './database/connection';
import { ExotelWebSocketServer } from './services/streaming/websocket-server';
import { AudioProcessor } from './services/streaming/audio-processor';
import { ConversationEngine } from './services/streaming/conversation-engine';
import { LiteLLMService } from './services/ai/litellm-service';

const app = express();
const server = createServer(app);

// Basic middleware
app.use(cors());
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// Health check endpoints
app.get('/health', async (req, res) => {
  const dbHealthy = await db.healthCheck().catch(() => false);

  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    database: dbHealthy ? 'connected' : 'disconnected'
  });
});

app.get('/api/v1/health', async (req, res) => {
  const dbHealthy = await db.healthCheck().catch(() => false);

  res.status(200).json({
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
    database: dbHealthy ? 'connected' : 'disconnected'
  });
});

// API routes
app.use('/api/v1/leads', leadsRouter);
app.use('/api/v1/calls', callsRouter);
app.use('/api/v1/conversation', conversationRouter);
app.use('/api/v1/streaming', streamingRouter);
app.use('/api/v1/dnd', dndRouter);
app.use('/api/v1/web-call', webCallRouter);

// Webhook routes (no versioning for external providers)
app.use('/webhook', webhooksRouter);

// Serve static files from frontend build
app.use(express.static(path.join(__dirname, '../public')));

// Serve React app for all non-API routes
app.get('*', (req, res) => {
  // Skip API routes and webhook routes
  if (req.path.startsWith('/api/') || req.path.startsWith('/webhook/') || req.path === '/health') {
    return res.status(404).json({ error: 'Route not found' });
  }

  return res.sendFile(path.join(__dirname, '../public/index.html'));
});

// Basic error handler
app.use((err: any, req: express.Request, res: express.Response, next: express.NextFunction) => {
  console.error('Error:', err);
  res.status(500).json({
    error: err.message || 'Internal server error'
  });
});

const PORT = process.env.PORT || 3000;

// Initialize database connection immediately when module is loaded
(async () => {
  try {
    await db.connect();
    console.log('Database connected successfully');
  } catch (error) {
    console.error('Failed to connect to database:', error);
  }
})();

async function startServer() {
  try {
    // Initialize AI services
    const litellm = new LiteLLMService({
      baseUrl: process.env.LITELLM_BASE_URL || 'http://localhost:4000',
      model: process.env.LITELLM_MODEL || 'gpt-3.5-turbo',
      timeout: 30000
    });

    // Initialize audio processor
    const audioProcessor = new AudioProcessor({
      sttProvider: (process.env.STT_PROVIDER as any) || 'mock',
      ttsProvider: (process.env.TTS_PROVIDER as any) || 'mock',
      openaiApiKey: process.env.OPENAI_API_KEY || undefined,
      deepgramApiKey: process.env.DEEPGRAM_API_KEY || undefined,
      elevenlabsApiKey: process.env.ELEVENLABS_API_KEY || undefined,
      voice: process.env.TTS_VOICE || 'alloy',
      language: process.env.STT_LANGUAGE || 'en'
    });

    // Initialize conversation engine
    const conversationEngine = new ConversationEngine(litellm, {
      maxHistoryLength: 20,
      defaultPersonality: 'professional',
      responseTimeout: 10000,
      enableIntentDetection: true,
      enableDataExtraction: true
    });

    // Initialize WebSocket server for Exotel streaming
    const wsServer = new ExotelWebSocketServer(
      server,
      {
        port: Number(PORT),
        path: '/exotel/stream',
        maxConnections: 100,
        sessionTimeout: 60 * 60 * 1000 // 1 hour
      },
      audioProcessor,
      conversationEngine
    );

    // Health check for audio services
    const audioHealthy = await audioProcessor.healthCheck();
    if (!audioHealthy) {
      console.warn('⚠️ Audio processor health check failed - using mock services');
    }

    // Start server
    server.listen(PORT, () => {
      console.log(`🚀 AI Telecalling Server running on port ${PORT}`);

      // HARDCODED FOR RAILWAY TESTING
      const baseUrl = 'https://tele-ai-production.up.railway.app';
      const wsUrl = 'wss://tele-ai-production.up.railway.app/exotel/stream';

      console.log(`📞 Health check: ${baseUrl}/health`);
      console.log(`📋 Leads API: ${baseUrl}/api/v1/leads`);
      console.log(`📞 Calls API: ${baseUrl}/api/v1/calls`);
      console.log(`🔗 Webhooks: ${baseUrl}/webhook/exotel/*`);
      console.log(`🎙️ WebSocket streaming: ${wsUrl}`);
      console.log(`🤖 Real-time AI conversations enabled!`);
    });

  } catch (error) {
    console.error('Failed to start server:', error);
    process.exit(1);
  }
}

// Handle graceful shutdown
process.on('SIGTERM', async () => {
  console.log('SIGTERM received, shutting down gracefully');
  await db.disconnect();
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('SIGINT received, shutting down gracefully');
  await db.disconnect();
  process.exit(0);
});

if (require.main === module) {
  startServer();
}

// Export both the app and the server creation function
export default app;
export { startServer };
