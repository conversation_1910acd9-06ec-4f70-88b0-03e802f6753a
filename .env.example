# Server Configuration
NODE_ENV=development
PORT=3000
API_BASE_URL=http://localhost:3000

# Database Configuration
DATABASE_URL=postgresql://username:password@localhost:5432/ai_telecalling_mvp
DB_HOST=localhost
DB_PORT=5432
DB_NAME=ai_telecalling_mvp
DB_USER=username
DB_PASSWORD=password

# Authentication
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=24h

# Telephony Provider Configuration
# Options: 'exotel', 'knowlarity', 'twilio', 'airtel'
TELEPHONY_PROVIDER=exotel

# Exotel Configuration (Primary Indian Provider - Most Reliable)
EXOTEL_API_KEY=your_exotel_api_key
EXOTEL_API_TOKEN=your_exotel_api_token
EXOTEL_ACCOUNT_SID=your_exotel_account_sid
EXOTEL_BASE_URL=https://api.exotel.com
EXOTEL_CALLER_ID=+91xxxxxxxxxx

# Knowlarity Configuration (Cost-effective Indian Provider)
KNOWLARITY_API_KEY=your_knowlarity_api_key
KNOWLARITY_API_SECRET=your_knowlarity_api_secret
KNOWLARITY_BASE_URL=https://api.knowlarity.com
KNOWLARITY_CALLER_ID=+91xxxxxxxxxx

# Twilio Configuration (International Provider)
TWILIO_ACCOUNT_SID=your_twilio_account_sid
TWILIO_AUTH_TOKEN=your_twilio_auth_token
TWILIO_PHONE_NUMBER=+**********

# Airtel IQ Configuration (Messaging Provider - NOT for voice calls)
AIRTEL_API_KEY=your_airtel_api_key
AIRTEL_API_SECRET=your_airtel_api_secret
AIRTEL_BASE_URL=https://api.airteliq.com
AIRTEL_CALLER_ID=+91xxxxxxxxxx

# LiteLLM Configuration
LITELLM_BASE_URL=http://localhost:4000
LITELLM_MODEL=gpt-3.5-turbo
LITELLM_API_KEY=your_litellm_api_key

# Audio Processing Configuration
STT_PROVIDER=mock
TTS_PROVIDER=mock
STT_LANGUAGE=en
TTS_VOICE=alloy

# OpenAI Configuration (for STT/TTS)
OPENAI_API_KEY=your_openai_api_key

# Deepgram Configuration (for STT)
DEEPGRAM_API_KEY=your_deepgram_api_key

# ElevenLabs Configuration (for TTS)
ELEVENLABS_API_KEY=your_elevenlabs_api_key

# Fallback Configuration
FALLBACK_AGENT_NUMBER=+**********

# Logging
LOG_LEVEL=info

# File Upload
MAX_FILE_SIZE=********
UPLOAD_DIR=uploads

# msgclub DND API Configuration
DND_API_BASE_URL=http://msg.msgclub.net
DND_API_ENDPOINT=/rest/services/sendSMS/getNdncNonNdncNumbers
DND_AUTH_KEY=d5ce6115694915cd9fa8df8536d330
DND_API_TIMEOUT=30000

# Legacy DND Configuration (for backward compatibility)
DND_BULK_ENDPOINT=/bulk-check
DND_SINGLE_ENDPOINT=/check
DND_API_TOKEN=your_api_token_here

# DND Processing Configuration
MAX_CONCURRENT_REQUESTS=5
RETRY_ATTEMPTS=3
RETRY_DELAY_MS=2000
DND_MAX_BATCH_SIZE=100
MAX_FILE_SIZE_MB=50
MAX_RECORDS_LIMIT=100000

# Test Configuration
TEST_DATABASE_URL=postgresql://username:password@localhost:5432/ai_telecalling_test
TEST_TWILIO_ACCOUNT_SID=test_account_sid
TEST_TWILIO_AUTH_TOKEN=test_auth_token
VERBOSE_TESTS=false
