/**
 * @fileoverview DND Monitoring and Health Check Service
 * @description Comprehensive monitoring, health checks, and performance tracking for DND persistence
 * <AUTHOR> System
 * @version 1.0.0
 */

import { DNDHealthCheckResult, DNDValidationStats } from '../types';
import { dndConnectionPool } from './dndConnectionPool';
import { dndTransactionManager } from './dndTransactionManager';
import { dndAuditLogger } from './dndAuditLogger';
import { createDNDPersistenceConfig } from '../config/dndPersistence';
import logger from './logger';

/**
 * Performance metrics for DND operations
 */
export interface DNDPerformanceMetrics {
  operationCounts: Record<string, number>;
  averageResponseTimes: Record<string, number>;
  errorRates: Record<string, number>;
  throughput: {
    requestsPerMinute: number;
    requestsPerHour: number;
  };
  resourceUsage: {
    memoryUsage: number;
    cpuUsage: number;
    databaseConnections: number;
  };
}

/**
 * System health status
 */
export interface SystemHealthStatus {
  overall: 'healthy' | 'degraded' | 'unhealthy';
  components: {
    database: 'healthy' | 'degraded' | 'unhealthy';
    connectionPool: 'healthy' | 'degraded' | 'unhealthy';
    auditLogging: 'healthy' | 'degraded' | 'unhealthy';
    performance: 'healthy' | 'degraded' | 'unhealthy';
  };
  details: Record<string, any>;
  timestamp: Date;
}

/**
 * Alert configuration
 */
export interface AlertConfig {
  enabled: boolean;
  thresholds: {
    errorRate: number;
    responseTime: number;
    connectionPoolUsage: number;
    memoryUsage: number;
  };
  notifications: {
    email?: string[];
    webhook?: string;
    slack?: string;
  };
}

/**
 * Comprehensive monitoring service for DND persistence operations
 */
export class DNDMonitoringService {
  private config: ReturnType<typeof createDNDPersistenceConfig>;
  private metrics: DNDPerformanceMetrics;
  private alertConfig: AlertConfig;
  private monitoringInterval?: NodeJS.Timeout;
  private healthCheckInterval?: NodeJS.Timeout;
  private operationHistory: Array<{ operation: string; timestamp: number; duration: number; success: boolean }> = [];
  private readonly maxHistorySize = 10000;

  /**
   * Creates a new monitoring service
   * @param {string} environment - Environment configuration
   */
  constructor(environment: string = process.env.NODE_ENV || 'development') {
    this.config = createDNDPersistenceConfig(environment);
    
    this.metrics = {
      operationCounts: {},
      averageResponseTimes: {},
      errorRates: {},
      throughput: {
        requestsPerMinute: 0,
        requestsPerHour: 0
      },
      resourceUsage: {
        memoryUsage: 0,
        cpuUsage: 0,
        databaseConnections: 0
      }
    };

    this.alertConfig = {
      enabled: this.config.performanceMonitoring,
      thresholds: {
        errorRate: 0.05, // 5%
        responseTime: 2000, // 2 seconds
        connectionPoolUsage: 0.8, // 80%
        memoryUsage: 0.9 // 90%
      },
      notifications: {
        email: process.env.ALERT_EMAIL?.split(','),
        webhook: process.env.ALERT_WEBHOOK,
        slack: process.env.ALERT_SLACK_WEBHOOK
      }
    };

    if (this.config.performanceMonitoring) {
      this.startMonitoring();
    }

    logger.info('DND Monitoring Service initialized', {
      environment,
      performanceMonitoring: this.config.performanceMonitoring,
      auditLogging: this.config.auditLogging
    });
  }

  /**
   * Records an operation for monitoring
   * @param {string} operation - Operation name
   * @param {number} duration - Operation duration in milliseconds
   * @param {boolean} success - Whether operation was successful
   * @param {Record<string, any>} metadata - Additional metadata
   */
  recordOperation(
    operation: string,
    duration: number,
    success: boolean,
    metadata: Record<string, any> = {}
  ): void {
    const timestamp = Date.now();
    
    // Update operation counts
    this.metrics.operationCounts[operation] = (this.metrics.operationCounts[operation] || 0) + 1;
    
    // Update average response times
    const currentAvg = this.metrics.averageResponseTimes[operation] || 0;
    const count = this.metrics.operationCounts[operation];
    this.metrics.averageResponseTimes[operation] = ((currentAvg * (count - 1)) + duration) / count;
    
    // Update error rates
    if (!success) {
      const errorCount = (this.metrics.errorRates[operation] || 0) + 1;
      this.metrics.errorRates[operation] = errorCount / count;
    } else {
      this.metrics.errorRates[operation] = (this.metrics.errorRates[operation] || 0) * (count - 1) / count;
    }
    
    // Add to operation history
    this.operationHistory.push({ operation, timestamp, duration, success });
    
    // Trim history if too large
    if (this.operationHistory.length > this.maxHistorySize) {
      this.operationHistory.shift();
    }
    
    // Check for alerts
    if (this.alertConfig.enabled) {
      this.checkAlerts(operation, duration, success);
    }
    
    // Log performance data
    if (this.config.performanceMonitoring) {
      logger.debug('Operation recorded', {
        operation,
        duration,
        success,
        metadata
      });
    }
  }

  /**
   * Performs comprehensive health check
   * @returns {Promise<SystemHealthStatus>} System health status
   */
  async performHealthCheck(): Promise<SystemHealthStatus> {
    const startTime = Date.now();
    
    try {
      const [
        databaseHealth,
        connectionPoolHealth,
        auditLoggingHealth,
        performanceHealth
      ] = await Promise.allSettled([
        this.checkDatabaseHealth(),
        this.checkConnectionPoolHealth(),
        this.checkAuditLoggingHealth(),
        this.checkPerformanceHealth()
      ]);

      const components = {
        database: this.getHealthFromPromise(databaseHealth),
        connectionPool: this.getHealthFromPromise(connectionPoolHealth),
        auditLogging: this.getHealthFromPromise(auditLoggingHealth),
        performance: this.getHealthFromPromise(performanceHealth)
      };

      const overall = this.calculateOverallHealth(components);
      
      const healthStatus: SystemHealthStatus = {
        overall,
        components,
        details: {
          checkDuration: Date.now() - startTime,
          metrics: this.metrics,
          connectionPoolStats: dndConnectionPool.getStats(),
          transactionStats: dndTransactionManager.getTransactionStats()
        },
        timestamp: new Date()
      };

      // Log health check
      if (this.config.auditLogging) {
        await dndAuditLogger.logHealthCheck(healthStatus.details);
      }

      logger.info('Health check completed', {
        overall,
        components,
        duration: Date.now() - startTime
      });

      return healthStatus;
      
    } catch (error) {
      logger.error('Health check failed', { error });
      
      return {
        overall: 'unhealthy',
        components: {
          database: 'unhealthy',
          connectionPool: 'unhealthy',
          auditLogging: 'unhealthy',
          performance: 'unhealthy'
        },
        details: {
          error: error instanceof Error ? error.message : String(error),
          checkDuration: Date.now() - startTime
        },
        timestamp: new Date()
      };
    }
  }

  /**
   * Gets current performance metrics
   * @returns {DNDPerformanceMetrics} Performance metrics
   */
  getMetrics(): DNDPerformanceMetrics {
    this.updateThroughputMetrics();
    this.updateResourceUsageMetrics();
    return { ...this.metrics };
  }

  /**
   * Gets operation statistics for a specific time window
   * @param {number} windowMs - Time window in milliseconds
   * @returns {object} Operation statistics
   */
  getOperationStats(windowMs: number = 3600000): object { // Default 1 hour
    const cutoff = Date.now() - windowMs;
    const recentOps = this.operationHistory.filter(op => op.timestamp > cutoff);
    
    const stats: Record<string, any> = {};
    
    recentOps.forEach(op => {
      if (!stats[op.operation]) {
        stats[op.operation] = {
          count: 0,
          successCount: 0,
          totalDuration: 0,
          minDuration: Infinity,
          maxDuration: 0
        };
      }
      
      const opStats = stats[op.operation];
      opStats.count++;
      if (op.success) opStats.successCount++;
      opStats.totalDuration += op.duration;
      opStats.minDuration = Math.min(opStats.minDuration, op.duration);
      opStats.maxDuration = Math.max(opStats.maxDuration, op.duration);
    });
    
    // Calculate derived metrics
    Object.keys(stats).forEach(operation => {
      const opStats = stats[operation];
      opStats.averageDuration = opStats.totalDuration / opStats.count;
      opStats.successRate = opStats.successCount / opStats.count;
      opStats.errorRate = 1 - opStats.successRate;
    });
    
    return stats;
  }

  /**
   * Starts monitoring intervals
   * @private
   */
  private startMonitoring(): void {
    // Start metrics collection
    this.monitoringInterval = setInterval(() => {
      this.updateMetrics();
    }, 60000); // Every minute
    
    // Start health checks
    this.healthCheckInterval = setInterval(async () => {
      await this.performHealthCheck();
    }, 300000); // Every 5 minutes
  }

  /**
   * Stops monitoring intervals
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    logger.info('DND Monitoring Service stopped');
  }

  /**
   * Updates metrics periodically
   * @private
   */
  private updateMetrics(): void {
    this.updateThroughputMetrics();
    this.updateResourceUsageMetrics();
    
    logger.debug('Metrics updated', this.metrics);
  }

  /**
   * Updates throughput metrics
   * @private
   */
  private updateThroughputMetrics(): void {
    const now = Date.now();
    const oneMinuteAgo = now - 60000;
    const oneHourAgo = now - 3600000;
    
    const recentMinute = this.operationHistory.filter(op => op.timestamp > oneMinuteAgo);
    const recentHour = this.operationHistory.filter(op => op.timestamp > oneHourAgo);
    
    this.metrics.throughput.requestsPerMinute = recentMinute.length;
    this.metrics.throughput.requestsPerHour = recentHour.length;
  }

  /**
   * Updates resource usage metrics
   * @private
   */
  private updateResourceUsageMetrics(): void {
    const memUsage = process.memoryUsage();
    this.metrics.resourceUsage.memoryUsage = memUsage.heapUsed / memUsage.heapTotal;
    
    const poolStats = dndConnectionPool.getStats();
    this.metrics.resourceUsage.databaseConnections = poolStats.totalConnections;
    
    // CPU usage would require additional monitoring (not implemented here for simplicity)
    this.metrics.resourceUsage.cpuUsage = 0;
  }

  /**
   * Checks database health
   * @private
   * @returns {Promise<'healthy' | 'degraded' | 'unhealthy'>}
   */
  private async checkDatabaseHealth(): Promise<'healthy' | 'degraded' | 'unhealthy'> {
    try {
      const isHealthy = await dndConnectionPool.healthCheck();
      const poolStats = dndConnectionPool.getStats();
      
      if (!isHealthy) return 'unhealthy';
      if (poolStats.connectionErrors > 5) return 'degraded';
      if (poolStats.averageQueryTime > 2000) return 'degraded';
      
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  /**
   * Checks connection pool health
   * @private
   * @returns {Promise<'healthy' | 'degraded' | 'unhealthy'>}
   */
  private async checkConnectionPoolHealth(): Promise<'healthy' | 'degraded' | 'unhealthy'> {
    try {
      const stats = dndConnectionPool.getStats();
      const usage = stats.totalConnections / 20; // Assuming max 20 connections
      
      if (usage > 0.9) return 'unhealthy';
      if (usage > 0.8) return 'degraded';
      if (stats.waitingClients > 5) return 'degraded';
      
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  /**
   * Checks audit logging health
   * @private
   * @returns {Promise<'healthy' | 'degraded' | 'unhealthy'>}
   */
  private async checkAuditLoggingHealth(): Promise<'healthy' | 'degraded' | 'unhealthy'> {
    if (!this.config.auditLogging) {
      return 'healthy'; // Not enabled, so considered healthy
    }
    
    try {
      // Test audit logging by attempting to log a health check event
      await dndAuditLogger.logEvent('health_check_test', { timestamp: new Date() });
      return 'healthy';
    } catch (error) {
      return 'unhealthy';
    }
  }

  /**
   * Checks performance health
   * @private
   * @returns {Promise<'healthy' | 'degraded' | 'unhealthy'>}
   */
  private async checkPerformanceHealth(): Promise<'healthy' | 'degraded' | 'unhealthy'> {
    const stats = this.getOperationStats(300000); // Last 5 minutes
    
    let degradedCount = 0;
    let unhealthyCount = 0;
    
    Object.values(stats).forEach((opStats: any) => {
      if (opStats.errorRate > 0.1) unhealthyCount++; // 10% error rate
      else if (opStats.errorRate > 0.05) degradedCount++; // 5% error rate
      
      if (opStats.averageDuration > 5000) unhealthyCount++; // 5 second response time
      else if (opStats.averageDuration > 2000) degradedCount++; // 2 second response time
    });
    
    if (unhealthyCount > 0) return 'unhealthy';
    if (degradedCount > 0) return 'degraded';
    
    return 'healthy';
  }

  /**
   * Gets health status from promise result
   * @private
   * @param {PromiseSettledResult<any>} result - Promise result
   * @returns {'healthy' | 'degraded' | 'unhealthy'}
   */
  private getHealthFromPromise(result: PromiseSettledResult<any>): 'healthy' | 'degraded' | 'unhealthy' {
    if (result.status === 'fulfilled') {
      return result.value;
    }
    return 'unhealthy';
  }

  /**
   * Calculates overall health from component health
   * @private
   * @param {object} components - Component health statuses
   * @returns {'healthy' | 'degraded' | 'unhealthy'}
   */
  private calculateOverallHealth(components: Record<string, 'healthy' | 'degraded' | 'unhealthy'>): 'healthy' | 'degraded' | 'unhealthy' {
    const statuses = Object.values(components);
    
    if (statuses.some(status => status === 'unhealthy')) {
      return 'unhealthy';
    }
    
    if (statuses.some(status => status === 'degraded')) {
      return 'degraded';
    }
    
    return 'healthy';
  }

  /**
   * Checks for alert conditions
   * @private
   * @param {string} operation - Operation name
   * @param {number} duration - Operation duration
   * @param {boolean} success - Operation success
   */
  private checkAlerts(operation: string, duration: number, success: boolean): void {
    // Check response time threshold
    if (duration > this.alertConfig.thresholds.responseTime) {
      this.sendAlert('slow_response', {
        operation,
        duration,
        threshold: this.alertConfig.thresholds.responseTime
      });
    }
    
    // Check error rate threshold
    const errorRate = this.metrics.errorRates[operation] || 0;
    if (errorRate > this.alertConfig.thresholds.errorRate) {
      this.sendAlert('high_error_rate', {
        operation,
        errorRate,
        threshold: this.alertConfig.thresholds.errorRate
      });
    }
  }

  /**
   * Sends alert notification
   * @private
   * @param {string} alertType - Type of alert
   * @param {Record<string, any>} data - Alert data
   */
  private sendAlert(alertType: string, data: Record<string, any>): void {
    logger.warn(`Alert: ${alertType}`, data);
    
    // Here you would implement actual alert sending (email, webhook, etc.)
    // For now, just log the alert
  }
}

/**
 * Singleton instance of monitoring service
 */
export const dndMonitoringService = new DNDMonitoringService();
