import <PERSON> from 'papa<PERSON><PERSON>';
import { DNDRecord } from '../types';

export interface CSVValidationResult {
  isValid: boolean;
  errors: string[];
  preview: any[];
  totalRows: number;
  headers: string[];
}

export interface CSVExportOptions {
  filename?: string;
  includeHeaders?: boolean;
  selectedColumns?: string[];
}

// Required columns for DND validation
const REQUIRED_COLUMNS = ['phone'];

// Validate CSV file structure and content
export const validateCSVFile = (file: File): Promise<CSVValidationResult> => {
  return new Promise((resolve) => {
    const errors: string[] = [];
    
    // Check file type
    if (!file.name.toLowerCase().endsWith('.csv')) {
      errors.push('File must be a CSV file (.csv extension)');
    }
    
    // Check file size (50MB limit)
    const maxSizeBytes = 50 * 1024 * 1024; // 50MB
    if (file.size > maxSizeBytes) {
      errors.push(`File size (${(file.size / 1024 / 1024).toFixed(2)}MB) exceeds the 50MB limit`);
    }
    
    // Parse CSV to validate structure
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      preview: 10, // Only parse first 10 rows for validation
      complete: (results) => {
        const headers = results.meta.fields || [];
        const data = results.data;
        
        // Check for required columns (case-insensitive)
        const normalizedHeaders = headers.map(h => h.toLowerCase().trim());
        const missingColumns = REQUIRED_COLUMNS.filter(
          col => !normalizedHeaders.includes(col.toLowerCase())
        );
        
        if (missingColumns.length > 0) {
          errors.push(`Missing required columns: ${missingColumns.join(', ')}`);
        }
        
        // Check for parsing errors
        if (results.errors.length > 0) {
          errors.push(`CSV parsing errors: ${results.errors.map(e => e.message).join(', ')}`);
        }
        
        // Estimate total rows (rough calculation)
        const avgRowSize = file.size / (data.length || 1);
        const estimatedRows = Math.floor(file.size / avgRowSize);
        
        // Check record limit (100,000 records)
        if (estimatedRows > 100000) {
          errors.push(`Estimated ${estimatedRows.toLocaleString()} records exceeds the 100,000 record limit`);
        }
        
        resolve({
          isValid: errors.length === 0,
          errors,
          preview: data.slice(0, 5), // Return first 5 rows for preview
          totalRows: estimatedRows,
          headers,
        });
      },
      error: (error) => {
        errors.push(`Failed to parse CSV: ${error.message}`);
        resolve({
          isValid: false,
          errors,
          preview: [],
          totalRows: 0,
          headers: [],
        });
      }
    });
  });
};

// Parse CSV file completely (for preview purposes)
export const parseCSVFile = (file: File): Promise<any[]> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          reject(new Error(`CSV parsing failed: ${results.errors.map(e => e.message).join(', ')}`));
        } else {
          resolve(results.data);
        }
      },
      error: (error) => {
        reject(error);
      }
    });
  });
};

// Export data to CSV
export const exportToCSV = (
  data: DNDRecord[], 
  options: CSVExportOptions = {}
): void => {
  const {
    filename = `dnd-results-${new Date().toISOString().split('T')[0]}.csv`,
    includeHeaders = true,
    selectedColumns
  } = options;
  
  let exportData = data;
  
  // Filter columns if specified
  if (selectedColumns && selectedColumns.length > 0) {
    exportData = data.map(row => {
      const filteredRow: any = {};
      selectedColumns.forEach(col => {
        if (col in row) {
          filteredRow[col] = (row as any)[col];
        }
      });
      return filteredRow;
    });
  }
  
  const csv = Papa.unparse(exportData, {
    header: includeHeaders,
  });
  
  // Create and trigger download
  const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  const url = URL.createObjectURL(blob);
  
  link.setAttribute('href', url);
  link.setAttribute('download', filename);
  link.style.visibility = 'hidden';
  
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  
  URL.revokeObjectURL(url);
};

// Export filtered data based on DND status
export const exportFilteredData = (
  data: DNDRecord[],
  filter: 'all' | 'DND' | 'Non-DND' | 'Error',
  filename?: string
): void => {
  let filteredData = data;
  let filterSuffix = '';
  
  switch (filter) {
    case 'DND':
      filteredData = data.filter(record => record.dnd_status === 'DND');
      filterSuffix = '-dnd-only';
      break;
    case 'Non-DND':
      filteredData = data.filter(record => record.dnd_status === 'Non-DND');
      filterSuffix = '-non-dnd-only';
      break;
    case 'Error':
      filteredData = data.filter(record => record.dnd_status === 'Error');
      filterSuffix = '-errors-only';
      break;
    default:
      filterSuffix = '-all-results';
  }
  
  const defaultFilename = `dnd-results${filterSuffix}-${new Date().toISOString().split('T')[0]}.csv`;
  
  exportToCSV(filteredData, {
    filename: filename || defaultFilename,
  });
};

// Format file size for display
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes';
  
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Validate phone number format (basic validation)
export const isValidPhoneNumber = (phone: string): boolean => {
  // Remove all non-digit characters except + at the beginning
  const cleaned = phone.replace(/[^\d+]/g, '');
  
  // Check if it's a valid international format or Indian format
  const internationalPattern = /^\+\d{10,15}$/;
  const indianPattern = /^(\+91|91|0)?[6-9]\d{9}$/;
  
  return internationalPattern.test(cleaned) || indianPattern.test(cleaned);
};

// Validate email format
export const isValidEmail = (email: string): boolean => {
  const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailPattern.test(email);
};
