import WebSocket from 'ws';
import { Server } from 'http';
import { StreamingSession } from './streaming-session';
import { AudioProcessor } from './audio-processor';
import { ConversationEngine } from './conversation-engine';
import logger from '../../utils/logger';

export interface ExotelWebSocketEvent {
  event: 'connected' | 'start' | 'media' | 'dtmf' | 'stop' | 'clear' | 'mark' | 'test';
  sequence_number?: number;
  stream_sid?: string;
  // Legacy fields for backward compatibility
  sequenceNumber?: string;
  callSid?: string;
  payload?: string;
  digit?: string;
  mark?: string;
  message?: string; // For test events
  start?: {
    stream_sid: string;
    call_sid: string;
    account_sid: string;
    to: string;
    from: string;
    custom_parameters?: Record<string, string>;
    media_format?: {
      encoding: string;
      sample_rate: string;
      bit_rate: string;
    };
    // Legacy field for backward compatibility
    callSid?: string;
  };
  media?: {
    chunk: number;
    timestamp: string;
    payload: string;
  };
  dtmf?: {
    digit: string;
    duration?: string;
  };
  stop?: {
    call_sid: string;
    account_sid: string;
    reason: string;
  };
}

export interface StreamingConfig {
  port: number;
  path: string;
  maxConnections: number;
  sessionTimeout: number; // in milliseconds
}

export class ExotelWebSocketServer {
  private wss: WebSocket.Server;
  private activeSessions: Map<string, StreamingSession> = new Map();
  private audioProcessor: AudioProcessor;
  private conversationEngine: ConversationEngine;
  private config: StreamingConfig;

  constructor(
    server: Server,
    config: StreamingConfig,
    audioProcessor: AudioProcessor,
    conversationEngine: ConversationEngine
  ) {
    this.config = config;
    this.audioProcessor = audioProcessor;
    this.conversationEngine = conversationEngine;

    // Create WebSocket server
    this.wss = new WebSocket.Server({
      server,
      path: config.path,
      maxPayload: 16 * 1024 * 1024, // 16MB max payload
    });

    this.setupWebSocketServer();
    logger.info(`🎙️ Exotel WebSocket server initialized on path: ${config.path}`);
    logger.info(`🔗 WebSocket server listening for connections on port ${config.port}${config.path}`);
    logger.info(`📡 WebSocket server ready to accept Exotel connections`);
  }

  private setupWebSocketServer(): void {
    this.wss.on('connection', (ws: WebSocket, request) => {
      const clientIP = request.socket.remoteAddress;
      const userAgent = request.headers['user-agent'];
      const origin = request.headers.origin;

      logger.info(`🔌 New WebSocket connection from ${clientIP}`);
      logger.info(`📱 User-Agent: ${userAgent}`);
      logger.info(`🌐 Origin: ${origin}`);
      logger.info(`🔗 Request URL: ${request.url}`);

      // Check connection limits
      if (this.activeSessions.size >= this.config.maxConnections) {
        logger.warn(`❌ Connection limit reached. Rejecting connection from ${clientIP}`);
        ws.close(1013, 'Server overloaded');
        return;
      }

      this.handleWebSocketConnection(ws, request);
    });

    this.wss.on('error', (error) => {
      logger.error('❌ WebSocket server error:', error);
    });

    // Add listening event
    this.wss.on('listening', () => {
      logger.info(`🎧 WebSocket server is now listening for connections`);
    });
  }

  private async handleWebSocketConnection(ws: WebSocket, request: any): Promise<void> {
    let sessionId: string | null = null;
    let session: StreamingSession | null = null;

    try {
      // Set up WebSocket event handlers
      ws.on('message', async (data: WebSocket.Data) => {
        // 🔍 FULL RAW LOGGING - Log every single message we receive
        const rawMessage = data.toString();
        logger.info('📨 Raw WebSocket message received:', rawMessage);

        try {
          const event: ExotelWebSocketEvent = JSON.parse(rawMessage);
          logger.info(`📨 Parsed event: ${event.event}`, {
            callSid: event.callSid || event.start?.call_sid,
            streamSid: event.stream_sid,
            sequenceNumber: event.sequence_number || event.sequenceNumber,
            eventType: event.event,
            hasPayload: !!(event.media?.payload || event.payload)
          });

          try {
            await this.handleExotelEvent(ws, event, session);
          } catch (eventError) {
            logger.error('❌ Error handling Exotel event:', {
              error: eventError,
              eventType: event.event,
              callSid: event.callSid || event.start?.call_sid
            });
            this.sendErrorResponse(ws, `Event handling error: ${event.event}`);
          }
        } catch (parseError) {
          logger.error('❌ JSON parsing failed for WebSocket message:', {
            error: parseError,
            rawMessage: rawMessage,
            messageLength: rawMessage.length,
            messageType: typeof rawMessage
          });
          this.sendErrorResponse(ws, 'Invalid message format');
        }
      });

      ws.on('close', (code: number, reason: string) => {
        logger.info(`🔌 WebSocket connection closed: ${code} - ${reason}`);
        if (sessionId && session) {
          this.cleanupSession(sessionId, session);
        }
      });

      ws.on('error', (error: Error) => {
        logger.error('❌ WebSocket connection error:', error);
        if (sessionId && session) {
          this.cleanupSession(sessionId, session);
        }
      });

      // Send initial connection acknowledgment
      this.sendEvent(ws, { event: 'connected' });

    } catch (error) {
      logger.error('❌ Error setting up WebSocket connection:', error);
      ws.close(1011, 'Server error');
    }
  }

  private async handleExotelEvent(
    ws: WebSocket,
    event: ExotelWebSocketEvent,
    session: StreamingSession | null
  ): Promise<void> {
    switch (event.event) {
      case 'connected':
        logger.info('🔌 Exotel WebSocket connected');
        // Send acknowledgment to Exotel
        this.sendAcknowledgment(ws, 'connected', event.stream_sid);
        break;

      case 'test':
        logger.info('🧪 Test event received from Exotel — connection OK');
        // Send acknowledgment for test event
        this.sendAcknowledgment(ws, 'test', event.stream_sid);
        break;

      case 'start':
        // Handle both new format (call_sid) and legacy format (callSid)
        const callSid = event.start?.call_sid || event.start?.callSid || event.callSid;
        const streamSid = event.stream_sid || event.start?.stream_sid;

        if (!callSid) {
          logger.error('❌ Start event missing call_sid/callSid');
          return;
        }

        logger.info('🎬 Streaming started', {
          callSid,
          streamSid,
          sequenceNumber: event.sequence_number || event.sequenceNumber,
          mediaFormat: event.start?.media_format,
          customParams: event.start?.custom_parameters
        });

        session = await this.createSession(ws, callSid);
        logger.info(`🎙️ Audio streaming started for call: ${callSid}`);
        // Send acknowledgment to confirm we're ready for audio
        this.sendAcknowledgment(ws, 'start', streamSid);
        break;

      case 'media':
        const payload = event.media?.payload || event.payload;
        if (!session || !payload) {
          logger.warn('❌ Media event without session or payload');
          return;
        }

        logger.debug('🎤 Audio chunk received', {
          chunk: event.media?.chunk,
          timestamp: event.media?.timestamp,
          payloadLength: payload.length,
          sequenceNumber: event.sequence_number || event.sequenceNumber
        });

        await this.processAudioChunk(session, payload);
        break;

      case 'dtmf':
        const digit = event.dtmf?.digit || event.digit;
        if (session && digit) {
          logger.info(`📞 DTMF received: ${digit}`);
          await session.handleDTMF(digit);
        }
        break;

      case 'stop':
        if (session) {
          logger.info('🛑 Streaming stopped', {
            callSid: session.callSid,
            reason: event.stop?.reason,
            sequenceNumber: event.sequence_number || event.sequenceNumber
          });
          await this.endSession(session);
        }
        break;

      case 'clear':
        if (session) {
          logger.info(`🔄 Clearing session context for call: ${session.callSid}`);
          await session.clearContext();
        }
        break;

      case 'mark':
        if (session && event.mark) {
          logger.info(`📍 Mark received: ${event.mark}`);
          await session.handleMark(event.mark);
        }
        break;

      default:
        logger.warn(`❓ Unknown event type: ${event.event}`);
        // Send acknowledgment even for unknown events to keep connection alive
        this.sendAcknowledgment(ws, event.event);
    }
  }

  private sendAcknowledgment(ws: WebSocket, eventType: string, streamSid?: string): void {
    try {
      const ack = JSON.stringify({
        event: 'ack',
        type: eventType,
        stream_sid: streamSid,
        timestamp: new Date().toISOString()
      });
      ws.send(ack);
      logger.debug(`✅ Sent acknowledgment for event: ${eventType}`, { streamSid });
    } catch (error) {
      logger.error('❌ Error sending acknowledgment:', error);
    }
  }

  private async createSession(ws: WebSocket, callSid: string): Promise<StreamingSession> {
    const session = new StreamingSession(
      callSid,
      ws,
      this.audioProcessor,
      this.conversationEngine
    );

    this.activeSessions.set(callSid, session);

    // Set session timeout
    setTimeout(() => {
      if (this.activeSessions.has(callSid)) {
        logger.warn(`⏰ Session timeout for call: ${callSid}`);
        this.cleanupSession(callSid, session);
      }
    }, this.config.sessionTimeout);

    await session.initialize();
    return session;
  }

  private async processAudioChunk(session: StreamingSession, base64Audio: string): Promise<void> {
    try {
      // Decode base64 audio to PCM buffer
      const audioBuffer = Buffer.from(base64Audio, 'base64');
      
      // Process audio chunk through the session
      await session.processAudioChunk(audioBuffer);
    } catch (error) {
      logger.error('❌ Error processing audio chunk:', error);
    }
  }

  private async endSession(session: StreamingSession): Promise<void> {
    try {
      await session.end();
      this.activeSessions.delete(session.callSid);
      logger.info(`✅ Session ended for call: ${session.callSid}`);
    } catch (error) {
      logger.error('❌ Error ending session:', error);
    }
  }

  private cleanupSession(sessionId: string, session: StreamingSession): void {
    try {
      session.cleanup();
      this.activeSessions.delete(sessionId);
      logger.info(`🧹 Cleaned up session: ${sessionId}`);
    } catch (error) {
      logger.error('❌ Error cleaning up session:', error);
    }
  }

  private sendEvent(ws: WebSocket, event: Partial<ExotelWebSocketEvent>): void {
    if (ws.readyState === WebSocket.OPEN) {
      ws.send(JSON.stringify(event));
    }
  }

  private sendErrorResponse(ws: WebSocket, message: string): void {
    this.sendEvent(ws, {
      event: 'mark',
      mark: `error:${message}`
    });
  }

  // Public methods for monitoring and management
  public getActiveSessionCount(): number {
    return this.activeSessions.size;
  }

  public getSessionInfo(callSid: string): StreamingSession | undefined {
    return this.activeSessions.get(callSid);
  }

  public async closeAllSessions(): Promise<void> {
    const promises = Array.from(this.activeSessions.values()).map(session => 
      this.endSession(session)
    );
    await Promise.all(promises);
  }

  public close(): void {
    this.wss.close();
    logger.info('🔌 WebSocket server closed');
  }
}
