import { useState, useCallback } from 'react';
import { dndApiClient } from '../services/dndApi';
import { validateCSVFile } from '../utils/csvProcessor';
import { DNDValidationResponse, DNDUploadState } from '../types';

export interface UseDndUploadReturn {
  uploadState: DNDUploadState;
  uploadFile: (file: File) => Promise<DNDValidationResponse | null>;
  resetUpload: () => void;
  validateFile: (file: File) => Promise<{ isValid: boolean; errors: string[]; preview: any[] }>;
  setFile: (file: File) => void;
}

export const useDndUpload = (): UseDndUploadReturn => {
  const [uploadState, setUploadState] = useState<DNDUploadState>({
    file: null,
    isUploading: false,
    progress: 0,
    error: null,
  });

  const validateFile = useCallback(async (file: File) => {
    try {
      const validation = await validateCSVFile(file);
      return {
        isValid: validation.isValid,
        errors: validation.errors,
        preview: validation.preview,
      };
    } catch (error) {
      return {
        isValid: false,
        errors: [`Validation failed: ${error instanceof Error ? error.message : 'Unknown error'}`],
        preview: [],
      };
    }
  }, []);

  const uploadFile = useCallback(async (file: File): Promise<DNDValidationResponse | null> => {
    try {
      // Reset state
      setUploadState({
        file,
        isUploading: true,
        progress: 0,
        error: null,
      });

      // Validate file first
      const validation = await validateFile(file);
      if (!validation.isValid) {
        setUploadState(prev => ({
          ...prev,
          isUploading: false,
          error: validation.errors.join(', '),
        }));
        return null;
      }

      // Upload file with progress tracking
      const response = await dndApiClient.uploadCSV(file, (progress) => {
        setUploadState(prev => ({
          ...prev,
          progress,
        }));
      });

      // Success
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        progress: 100,
      }));

      return response;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
      }));
      return null;
    }
  }, [validateFile]);

  const resetUpload = useCallback(() => {
    setUploadState({
      file: null,
      isUploading: false,
      progress: 0,
      error: null,
    });
  }, []);

  const setFile = useCallback((file: File) => {
    setUploadState(prev => ({
      ...prev,
      file,
      error: null,
    }));
  }, []);

  return {
    uploadState,
    uploadFile,
    resetUpload,
    validateFile,
    setFile,
  };
};
