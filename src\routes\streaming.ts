import { Router } from 'express';
import { ExotelStreamingFlow, ExoMLTemplates } from '../services/exotel/streaming-flow';
import { CallModel } from '../database/models/Call';
import { LeadModel } from '../database/models/Lead';
import logger from '../utils/logger';

const router = Router();

// Get WebSocket URL based on environment
function getWebSocketUrl(): string {
  // HARDCODED FOR RAILWAY TESTING
  const railwayUrl = 'https://tele-ai-production.up.railway.app';
  return ExotelStreamingFlow.getWebSocketUrl(railwayUrl);
}

// Initialize streaming flow
function createStreamingFlow(): ExotelStreamingFlow {
  return new ExotelStreamingFlow({
    websocketUrl: getWebSocketUrl(),
    enableRecording: true,
    maxDuration: 3600, // 1 hour max
    fallbackNumber: process.env.FALLBACK_AGENT_NUMBER || undefined
  });
}

/**
 * GET /api/v1/streaming/exoml/direct
 * Direct AI conversation - immediately starts WebSocket streaming
 */
router.get('/exoml/direct', async (req, res) => {
  try {
    logger.info('📞 Generating direct AI ExoML');
    
    const streamingFlow = createStreamingFlow();
    const exoml = streamingFlow.generateDirectAIExoML();
    
    res.set('Content-Type', 'application/xml');
    res.send(exoml);
    
  } catch (error) {
    logger.error('❌ Error generating direct AI ExoML:', error);
    res.set('Content-Type', 'application/xml');
    res.send(ExoMLTemplates.error('Service temporarily unavailable'));
  }
});

/**
 * GET /api/v1/streaming/exoml/hybrid
 * Hybrid flow - customer chooses AI or human
 */
router.get('/exoml/hybrid', async (req, res) => {
  try {
    logger.info('📞 Generating hybrid ExoML');
    
    const streamingFlow = createStreamingFlow();
    const exoml = streamingFlow.generateHybridExoML();
    
    res.set('Content-Type', 'application/xml');
    res.send(exoml);
    
  } catch (error) {
    logger.error('❌ Error generating hybrid ExoML:', error);
    res.set('Content-Type', 'application/xml');
    res.send(ExoMLTemplates.error('Service temporarily unavailable'));
  }
});

/**
 * POST /api/v1/streaming/exoml/dtmf
 * Handle DTMF input for hybrid flow
 */
router.post('/exoml/dtmf', async (req, res) => {
  try {
    const { Digits, CallSid } = req.body;
    
    logger.info(`📞 DTMF input received: ${Digits} for call: ${CallSid}`);
    
    const streamingFlow = createStreamingFlow();
    const exoml = streamingFlow.generateDTMFHandlerExoML(Digits);
    
    res.set('Content-Type', 'application/xml');
    res.send(exoml);
    
  } catch (error) {
    logger.error('❌ Error handling DTMF input:', error);
    res.set('Content-Type', 'application/xml');
    res.send(ExoMLTemplates.error('Invalid input. Please try again.', true));
  }
});

/**
 * GET /api/v1/streaming/exoml/transcribe
 * Transcription-only flow using Stream Applet
 */
router.get('/exoml/transcribe', async (req, res) => {
  try {
    logger.info('📞 Generating transcription ExoML');

    const streamingFlow = createStreamingFlow();
    const exoml = streamingFlow.generateStreamExoML();

    res.set('Content-Type', 'application/xml');
    res.send(exoml);

  } catch (error) {
    logger.error('❌ Error generating transcription ExoML:', error);
    res.set('Content-Type', 'application/xml');
    res.send(ExoMLTemplates.error('Transcription service unavailable'));
  }
});

/**
 * GET /api/v1/streaming/exoml/debug
 * Simple debug ExoML for testing WebSocket connectivity
 * Returns minimal Stream applet to test start/media events
 */
router.get('/exoml/debug', async (_req, res) => {
  try {
    logger.info('🔍 Generating debug ExoML for WebSocket testing');

    const websocketUrl = getWebSocketUrl();

    // Minimal ExoML for debugging - just Stream applet
    const debugExoML = `<?xml version="1.0" encoding="UTF-8"?>
<Response>
  <Start>
    <Stream url="${websocketUrl}" />
  </Start>
</Response>`;

    logger.info(`🔍 Debug ExoML generated with WebSocket URL: ${websocketUrl}`);

    res.set('Content-Type', 'application/xml');
    res.send(debugExoML);

  } catch (error) {
    logger.error('❌ Error generating debug ExoML:', error);
    res.set('Content-Type', 'application/xml');
    res.send(ExoMLTemplates.error('Debug service unavailable'));
  }
});

/**
 * GET /api/v1/exotel/passthru
 * Handle post-conversation routing decisions
 * 🔥 BEST PRACTICE: Monitor StreamSID, Duration, Status, and DisconnectedBy via Passthru
 */
router.get('/exotel/passthru', async (req, res) => {
  try {
    const {
      CallSid,
      RecordingUrl,
      Duration,
      StreamSID,
      Status,
      DisconnectedBy,
      // Additional Exotel parameters
      From,
      To,
      Direction,
      CallType
    } = req.query;

    logger.info(`📞 Passthru called for call: ${CallSid}`, {
      streamSid: StreamSID,
      duration: Duration,
      status: Status,
      disconnectedBy: DisconnectedBy,
      from: From,
      to: To,
      direction: Direction,
      callType: CallType,
      recordingUrl: RecordingUrl
    });

    // Update call record with final data
    if (CallSid) {
      try {
        const call = await CallModel.findByCallSid(CallSid as string);
        if (call) {
          const updateData: any = {
            status: 'completed'
          };

          if (Duration) {
            updateData.duration = parseInt(Duration as string);
          }

          if (RecordingUrl) {
            updateData.recordingUrl = RecordingUrl as string;
          }

          // Store additional streaming metadata
          if (StreamSID || Status || DisconnectedBy) {
            updateData.metadata = JSON.stringify({
              streamSid: StreamSID,
              streamStatus: Status,
              disconnectedBy: DisconnectedBy,
              direction: Direction,
              callType: CallType
            });
          }

          await CallModel.update(CallSid as string, updateData);

          logger.info(`✅ Updated call record with streaming metadata: ${call.id}`);
        }
      } catch (dbError) {
        logger.error('❌ Error updating call record:', dbError);
      }
    }

    // Default response - no escalation
    res.json({
      escalate: false,
      callback: false,
      message: 'Call completed successfully'
    });

  } catch (error) {
    logger.error('❌ Error in passthru handler:', error);
    res.status(500).json({
      escalate: true,
      message: 'Error processing call'
    });
  }
});

/**
 * POST /api/v1/streaming/initiate
 * Initiate a new streaming call
 */
router.post('/initiate', async (req, res): Promise<void> => {
  try {
    const { leadId, phoneNumber, flowType = 'direct' } = req.body;
    
    if (!phoneNumber) {
      res.status(400).json({ error: 'Phone number is required' });
      return;
    }
    
    logger.info(`📞 Initiating streaming call to: ${phoneNumber}, flow: ${flowType}`);
    
    // Create call record with temporary callSid
    const tempCallSid = `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    const call = await CallModel.create({
      leadId: leadId || 0,
      callSid: tempCallSid,
      status: 'initiated',
      direction: 'outbound',
      initialMessage: `Streaming call - ${flowType} flow`
    });
    
    // Determine ExoML URL based on flow type
    const baseUrl = process.env.API_BASE_URL || `${req.protocol}://${req.get('host')}`;
    let exomlUrl: string;
    
    switch (flowType) {
      case 'hybrid':
        exomlUrl = `${baseUrl}/api/v1/streaming/exoml/hybrid`;
        break;
      case 'transcribe':
        exomlUrl = `${baseUrl}/api/v1/streaming/exoml/transcribe`;
        break;
      case 'direct':
      default:
        exomlUrl = `${baseUrl}/api/v1/streaming/exoml/direct`;
        break;
    }
    
    // Make Exotel API call to initiate the call
    const exotelResponse = await fetch(`https://api.exotel.com/v1/Accounts/${process.env.EXOTEL_SID}/Calls/connect.json`, {
      method: 'POST',
      headers: {
        'Authorization': `Basic ${Buffer.from(`${process.env.EXOTEL_SID}:${process.env.EXOTEL_TOKEN}`).toString('base64')}`,
        'Content-Type': 'application/x-www-form-urlencoded'
      },
      body: new URLSearchParams({
        From: process.env.EXOTEL_CALLER_ID || '',
        To: phoneNumber,
        Url: exomlUrl,
        Method: 'GET'
      })
    });
    
    if (!exotelResponse.ok) {
      throw new Error(`Exotel API error: ${exotelResponse.status}`);
    }
    
    const exotelData = await exotelResponse.json() as any;
    const callSid = exotelData.Call?.Sid;
    
    if (callSid) {
      // Update call record with real Exotel SID
      await CallModel.update(tempCallSid, {
        status: 'in-progress'
      });

      // Update with real callSid by creating new record and deleting temp
      await CallModel.delete(tempCallSid);
      const updatedCall = await CallModel.create({
        leadId: leadId || 0,
        callSid,
        status: 'in-progress',
        direction: 'outbound',
        initialMessage: `Streaming call - ${flowType} flow`
      });
      
      logger.info(`✅ Call initiated successfully: ${callSid}`);
    }
    
    res.json({
      success: true,
      callId: call.id,
      callSid,
      exomlUrl,
      websocketUrl: getWebSocketUrl(),
      message: 'Streaming call initiated successfully'
    });
    
  } catch (error) {
    logger.error('❌ Error initiating streaming call:', error);
    res.status(500).json({
      error: 'Failed to initiate call',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/v1/streaming/status
 * Get streaming service status
 */
router.get('/status', async (req, res) => {
  try {
    const websocketUrl = getWebSocketUrl();
    const isValidWsUrl = ExotelStreamingFlow.validateWebSocketUrl(websocketUrl);
    
    res.json({
      status: 'operational',
      websocketUrl,
      websocketValid: isValidWsUrl,
      features: {
        directAI: true,
        hybridFlow: true,
        transcriptionOnly: true,
        recording: true
      },
      endpoints: {
        directAI: `https://tele-ai-production.up.railway.app/api/v1/streaming/exoml/direct`,
        hybrid: `https://tele-ai-production.up.railway.app/api/v1/streaming/exoml/hybrid`,
        transcribe: `https://tele-ai-production.up.railway.app/api/v1/streaming/exoml/transcribe`,
        debug: `https://tele-ai-production.up.railway.app/api/v1/streaming/exoml/debug`
      },
      debug: {
        railwayDomain: 'tele-ai-production.up.railway.app',
        hardcodedUrls: true,
        timestamp: new Date().toISOString()
      }
    });
    
  } catch (error) {
    logger.error('❌ Error getting streaming status:', error);
    res.status(500).json({
      status: 'error',
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/v1/streaming/test-websocket
 * Simple WebSocket test page for debugging Railway connectivity
 */
router.get('/test-websocket', (req, res) => {
  const html = `
<!DOCTYPE html>
<html>
<head>
    <title>WebSocket Test - Railway</title>
</head>
<body>
    <h1>WebSocket Connection Test</h1>
    <div id="status">Connecting...</div>
    <div id="messages"></div>

    <script>
        const wsUrl = 'wss://tele-ai-production.up.railway.app/exotel/stream';
        const statusDiv = document.getElementById('status');
        const messagesDiv = document.getElementById('messages');

        function log(message) {
            const div = document.createElement('div');
            div.textContent = new Date().toISOString() + ': ' + message;
            messagesDiv.appendChild(div);
        }

        log('Attempting to connect to: ' + wsUrl);

        const ws = new WebSocket(wsUrl);

        ws.onopen = function() {
            statusDiv.textContent = 'Connected!';
            statusDiv.style.color = 'green';
            log('WebSocket connected successfully');

            // Send a test message
            ws.send(JSON.stringify({
                event: 'test',
                message: 'Hello from browser'
            }));
        };

        ws.onmessage = function(event) {
            log('Received: ' + event.data);
        };

        ws.onerror = function(error) {
            statusDiv.textContent = 'Error!';
            statusDiv.style.color = 'red';
            log('WebSocket error: ' + error);
        };

        ws.onclose = function(event) {
            statusDiv.textContent = 'Disconnected';
            statusDiv.style.color = 'orange';
            log('WebSocket closed: ' + event.code + ' - ' + event.reason);
        };
    </script>
</body>
</html>`;

  res.send(html);
});

export default router;
