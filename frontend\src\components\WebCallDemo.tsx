import React, { useState, useRef, useEffect } from 'react';
import { Button } from './ui/Button';
import { Card } from './ui/Card';
import { Badge } from './ui/Badge';
import { Mic, MicOff, Volume2, VolumeX, Phone, PhoneOff, Activity, Bot, User } from 'lucide-react';
import { WebAudioService } from '../services/WebAudioService';

interface WebCallMessage {
  speaker: 'user' | 'ai';
  text: string;
  timestamp: Date;
  confidence?: number;
}

interface WebCallDemoProps {
  className?: string;
}

export const WebCallDemo: React.FC<WebCallDemoProps> = ({ className = '' }) => {
  const [isCallActive, setIsCallActive] = useState(false);
  const [isRecording, setIsRecording] = useState(false);
  const [isSpeaking, setIsSpeaking] = useState(false);
  const [messages, setMessages] = useState<WebCallMessage[]>([]);
  const [callDuration, setCallDuration] = useState(0);
  const [audioLevel, setAudioLevel] = useState(0);
  const [connectionStatus, setConnectionStatus] = useState<'disconnected' | 'connecting' | 'connected'>('disconnected');
  const [sessionId, setSessionId] = useState<string>('');

  const webAudioServiceRef = useRef<WebAudioService | null>(null);
  const callStartTimeRef = useRef<Date | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const currentRecordingRef = useRef<Promise<Blob> | null>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Call duration timer
  useEffect(() => {
    let interval: NodeJS.Timeout;
    if (isCallActive && callStartTimeRef.current) {
      interval = setInterval(() => {
        const now = new Date();
        const duration = Math.floor((now.getTime() - callStartTimeRef.current!.getTime()) / 1000);
        setCallDuration(duration);
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isCallActive]);

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const startWebCall = async () => {
    try {
      setConnectionStatus('connecting');

      // Generate session ID for this call
      const newSessionId = 'web-demo-' + Date.now();
      setSessionId(newSessionId);

      // Check browser support
      if (!WebAudioService.isSupported()) {
        throw new Error('Your browser does not support required audio features');
      }

      // Initialize WebAudioService
      webAudioServiceRef.current = new WebAudioService({
        sampleRate: 16000,
        channels: 1,
        echoCancellation: true,
        noiseSuppression: true,
        autoGainControl: true
      });

      await webAudioServiceRef.current.initialize();

      // Set up audio level monitoring
      webAudioServiceRef.current.setAudioLevelCallback((level) => {
        setAudioLevel(level);
      });

      setIsCallActive(true);
      setConnectionStatus('connected');
      callStartTimeRef.current = new Date();

      // Start with AI greeting
      await sendInitialGreeting(newSessionId);

      console.log('🎙️ Web call started successfully');
    } catch (error) {
      console.error('❌ Failed to start web call:', error);
      setConnectionStatus('disconnected');
      alert(`Failed to start web call: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  };

  const endWebCall = () => {
    // Clean up WebAudioService
    if (webAudioServiceRef.current) {
      webAudioServiceRef.current.cleanup();
      webAudioServiceRef.current = null;
    }

    // Reset current recording
    currentRecordingRef.current = null;

    setIsCallActive(false);
    setIsRecording(false);
    setIsSpeaking(false);
    setConnectionStatus('disconnected');
    setAudioLevel(0);

    console.log('📞 Web call ended');
  };

  const sendInitialGreeting = async (currentSessionId?: string) => {
    try {
      setIsSpeaking(true);

      const response = await fetch('/api/v1/web-call/greeting', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ sessionId: currentSessionId || sessionId })
      });

      const data = await response.json();
      
      if (data.success) {
        // Add AI message to transcript
        setMessages(prev => [...prev, {
          speaker: 'ai',
          text: data.greeting,
          timestamp: new Date()
        }]);

        // Play AI audio if available
        if (data.audioUrl && webAudioServiceRef.current) {
          await webAudioServiceRef.current.playAudio(data.audioUrl);
        }
      }
    } catch (error) {
      console.error('❌ Failed to get initial greeting:', error);
    } finally {
      setIsSpeaking(false);
    }
  };

  const startRecording = async () => {
    if (!webAudioServiceRef.current || isRecording) {
      console.warn('⚠️ Cannot start recording: service not available or already recording');
      return;
    }

    try {
      // Resume audio context if needed
      await webAudioServiceRef.current.resumeAudioContext();

      // Start recording and store the promise
      currentRecordingRef.current = webAudioServiceRef.current.startRecording();
      setIsRecording(true);
      console.log('🎤 Recording started');
    } catch (error) {
      console.error('❌ Failed to start recording:', error);
    }
  };

  const stopRecording = async () => {
    if (!webAudioServiceRef.current || !isRecording || !currentRecordingRef.current) {
      console.warn('⚠️ Cannot stop recording: not currently recording');
      return;
    }

    try {
      webAudioServiceRef.current.stopRecording();
      const audioBlob = await currentRecordingRef.current;
      setIsRecording(false);
      currentRecordingRef.current = null;

      console.log('🎤 Recording stopped, processing audio...');
      await processAudioBlob(audioBlob);
    } catch (error) {
      console.error('❌ Failed to stop recording:', error);
      setIsRecording(false);
      currentRecordingRef.current = null;
    }
  };

  const processAudioBlob = async (audioBlob: Blob) => {
    try {
      console.log('🎤 Processing audio blob:', {
        size: audioBlob.size,
        type: audioBlob.type
      });
      console.log('🎤 About to make fetch request to /api/v1/web-call/process-audio');

      if (audioBlob.size === 0) {
        console.error('❌ Audio blob is empty!');
        return;
      }

      const formData = new FormData();
      formData.append('audio', audioBlob, 'audio.webm');
      formData.append('sessionId', sessionId || 'web-demo-' + Date.now());

      const response = await fetch('/api/v1/web-call/process-audio', {
        method: 'POST',
        body: formData
      });

      const data = await response.json();

      if (data.success && data.transcription) {
        // Add user message
        setMessages(prev => [...prev, {
          speaker: 'user',
          text: data.transcription,
          timestamp: new Date(),
          confidence: data.confidence
        }]);

        // Add AI response if available
        if (data.aiResponse) {
          setIsSpeaking(true);

          setMessages(prev => [...prev, {
            speaker: 'ai',
            text: data.aiResponse,
            timestamp: new Date()
          }]);

          // Play AI audio response
          if (data.audioUrl && webAudioServiceRef.current) {
            await webAudioServiceRef.current.playAudio(data.audioUrl);
          }

          setIsSpeaking(false);
        }
      }
    } catch (error) {
      console.error('❌ Failed to process audio:', error);
      setIsSpeaking(false);
    }
  };

  // Cleanup on component unmount
  useEffect(() => {
    return () => {
      if (webAudioServiceRef.current) {
        webAudioServiceRef.current.cleanup();
      }
    };
  }, []);

  return (
    <Card className={`${className}`}>
      <div className="p-6">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-xl font-semibold flex items-center">
              <Phone className="h-5 w-5 mr-2" />
              Web Call Demo
            </h2>
            <p className="text-sm text-gray-600 mt-1">
              Test AI conversation with real STT/TTS through your browser
            </p>
          </div>
          
          <div className="flex items-center space-x-3">
            {isCallActive && (
              <Badge className="bg-green-100 text-green-800">
                <Activity className="h-3 w-3 mr-1" />
                {formatDuration(callDuration)}
              </Badge>
            )}
            
            <Badge className={
              connectionStatus === 'connected' ? 'bg-green-100 text-green-800' :
              connectionStatus === 'connecting' ? 'bg-yellow-100 text-yellow-800' :
              'bg-gray-100 text-gray-800'
            }>
              {connectionStatus}
            </Badge>
          </div>
        </div>

        {/* Call Controls */}
        <div className="flex items-center justify-center space-x-4 mb-6">
          {!isCallActive ? (
            <Button
              onClick={startWebCall}
              className="bg-green-600 hover:bg-green-700 text-white px-8 py-3"
              disabled={connectionStatus === 'connecting'}
            >
              <Phone className="h-5 w-5 mr-2" />
              Start Web Call
            </Button>
          ) : (
            <>
              <Button
                onClick={isRecording ? stopRecording : startRecording}
                className={`px-6 py-3 ${
                  isRecording 
                    ? 'bg-red-600 hover:bg-red-700 text-white' 
                    : 'bg-blue-600 hover:bg-blue-700 text-white'
                }`}
              >
                {isRecording ? <MicOff className="h-5 w-5 mr-2" /> : <Mic className="h-5 w-5 mr-2" />}
                {isRecording ? 'Stop Recording' : 'Start Recording'}
              </Button>
              
              <Button
                onClick={endWebCall}
                className="bg-red-600 hover:bg-red-700 text-white px-6 py-3"
              >
                <PhoneOff className="h-5 w-5 mr-2" />
                End Call
              </Button>
            </>
          )}
        </div>

        {/* Status Indicators */}
        {isCallActive && (
          <div className="flex items-center justify-center space-x-6 mb-6 text-sm">
            <div className={`flex items-center ${isRecording ? 'text-red-600' : 'text-gray-400'}`}>
              <Mic className="h-4 w-4 mr-1" />
              {isRecording ? 'Recording...' : 'Not Recording'}
            </div>
            
            <div className={`flex items-center ${isSpeaking ? 'text-blue-600' : 'text-gray-400'}`}>
              {isSpeaking ? <Volume2 className="h-4 w-4 mr-1" /> : <VolumeX className="h-4 w-4 mr-1" />}
              {isSpeaking ? 'AI Speaking...' : 'AI Silent'}
            </div>
          </div>
        )}

        {/* Conversation Transcript */}
        <div className="border rounded-lg h-96 overflow-y-auto bg-gray-50 p-4">
          {messages.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500">
              <div className="text-center">
                <Phone className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>Start a web call to see the conversation</p>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              {messages.map((message, index) => (
                <div
                  key={index}
                  className={`flex ${message.speaker === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
                      message.speaker === 'user'
                        ? 'bg-blue-600 text-white'
                        : 'bg-white text-gray-900 border'
                    }`}
                  >
                    <div className="flex items-center mb-1">
                      {message.speaker === 'user' ? (
                        <User className="h-4 w-4 mr-2" />
                      ) : (
                        <Bot className="h-4 w-4 mr-2" />
                      )}
                      <span className="text-xs opacity-75">
                        {message.timestamp.toLocaleTimeString()}
                      </span>
                      {message.confidence && (
                        <span className="text-xs opacity-75 ml-2">
                          ({Math.round(message.confidence * 100)}%)
                        </span>
                      )}
                    </div>
                    <p className="text-sm">{message.text}</p>
                  </div>
                </div>
              ))}
              <div ref={messagesEndRef} />
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h3 className="font-medium text-blue-900 mb-2">How to Use:</h3>
          <ol className="text-sm text-blue-800 space-y-1">
            <li>1. Click "Start Web Call" to begin (allow microphone access)</li>
            <li>2. AI will greet you with real OpenAI TTS voice</li>
            <li>3. Click "Start Recording" and speak your response</li>
            <li>4. Click "Stop Recording" when done speaking</li>
            <li>5. AI will process with real STT and respond with real TTS</li>
          </ol>
        </div>
      </div>
    </Card>
  );
};
