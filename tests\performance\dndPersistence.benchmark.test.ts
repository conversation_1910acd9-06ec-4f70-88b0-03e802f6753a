/**
 * @fileoverview Performance Benchmark Tests for DND Persistence
 * @description Comprehensive performance testing and benchmarking for DND operations
 */

import { DNDPersistenceService } from '@/services/dndPersistenceService';
import { DNDValidationModel } from '@/database/models/DNDValidation';
import { db } from '@/database/connection';
import { dndMonitoringService } from '@/utils/dndMonitoringService';

describe('DND Persistence Performance Benchmarks', () => {
  let persistenceService: DNDPersistenceService;
  let startTime: number;

  beforeAll(async () => {
    await db.connect();
    persistenceService = new DNDPersistenceService();
  });

  afterAll(async () => {
    // Clean up test data
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['perf%']);
    await db.disconnect();
  });

  beforeEach(() => {
    startTime = Date.now();
  });

  afterEach(() => {
    const duration = Date.now() - startTime;
    console.log(`Test completed in ${duration}ms`);
  });

  describe('Single Operation Benchmarks', () => {
    it('should save single validation within performance threshold', async () => {
      const result = {
        phone: 'perf9876543210',
        dnd_status: 'DND' as const
      };

      const start = performance.now();
      await persistenceService.saveDNDValidation(result);
      const duration = performance.now() - start;

      expect(duration).toBeLessThan(100); // Should complete within 100ms
      
      // Record performance metric
      dndMonitoringService.recordOperation('saveDNDValidation', duration, true);
    });

    it('should retrieve validation within performance threshold', async () => {
      // Setup test data
      await persistenceService.saveDNDValidation({
        phone: 'perf9876543211',
        dnd_status: 'Non-DND' as const
      });

      const start = performance.now();
      const validation = await persistenceService.getDNDValidation('perf9876543211');
      const duration = performance.now() - start;

      expect(validation).not.toBeNull();
      expect(duration).toBeLessThan(50); // Should complete within 50ms
      
      dndMonitoringService.recordOperation('getDNDValidation', duration, true);
    });

    it('should check recent validation within performance threshold', async () => {
      // Setup test data
      await persistenceService.saveDNDValidation({
        phone: 'perf9876543212',
        dnd_status: 'DND' as const
      });

      const start = performance.now();
      const isRecent = await persistenceService.isRecentlyValidated('perf9876543212');
      const duration = performance.now() - start;

      expect(isRecent).toBe(true);
      expect(duration).toBeLessThan(30); // Should complete within 30ms
      
      dndMonitoringService.recordOperation('isRecentlyValidated', duration, true);
    });
  });

  describe('Bulk Operation Benchmarks', () => {
    it('should handle small bulk operations efficiently', async () => {
      const results = Array.from({ length: 10 }, (_, i) => ({
        phone: `perf987654${i.toString().padStart(4, '0')}`,
        dnd_status: i % 2 === 0 ? 'DND' as const : 'Non-DND' as const
      }));

      const start = performance.now();
      const saved = await persistenceService.saveBulkDNDValidations(results);
      const duration = performance.now() - start;

      expect(saved).toHaveLength(10);
      expect(duration).toBeLessThan(500); // Should complete within 500ms
      
      const throughput = results.length / (duration / 1000); // Operations per second
      expect(throughput).toBeGreaterThan(20); // At least 20 ops/sec
      
      dndMonitoringService.recordOperation('saveBulkDNDValidations_small', duration, true, {
        count: results.length,
        throughput
      });
    });

    it('should handle medium bulk operations efficiently', async () => {
      const results = Array.from({ length: 100 }, (_, i) => ({
        phone: `perf988654${i.toString().padStart(4, '0')}`,
        dnd_status: i % 3 === 0 ? 'DND' as const : i % 3 === 1 ? 'Non-DND' as const : 'Error' as const
      }));

      const start = performance.now();
      const saved = await persistenceService.saveBulkDNDValidations(results);
      const duration = performance.now() - start;

      expect(saved).toHaveLength(100);
      expect(duration).toBeLessThan(2000); // Should complete within 2 seconds
      
      const throughput = results.length / (duration / 1000);
      expect(throughput).toBeGreaterThan(50); // At least 50 ops/sec
      
      dndMonitoringService.recordOperation('saveBulkDNDValidations_medium', duration, true, {
        count: results.length,
        throughput
      });
    });

    it('should handle large bulk operations within acceptable time', async () => {
      const results = Array.from({ length: 1000 }, (_, i) => ({
        phone: `perf989654${i.toString().padStart(4, '0')}`,
        dnd_status: i % 2 === 0 ? 'DND' as const : 'Non-DND' as const
      }));

      const start = performance.now();
      const saved = await persistenceService.saveBulkDNDValidations(results);
      const duration = performance.now() - start;

      expect(saved).toHaveLength(1000);
      expect(duration).toBeLessThan(10000); // Should complete within 10 seconds
      
      const throughput = results.length / (duration / 1000);
      expect(throughput).toBeGreaterThan(100); // At least 100 ops/sec
      
      dndMonitoringService.recordOperation('saveBulkDNDValidations_large', duration, true, {
        count: results.length,
        throughput
      });
    });
  });

  describe('Query Performance Benchmarks', () => {
    beforeAll(async () => {
      // Setup test data for query benchmarks
      const testData = Array.from({ length: 500 }, (_, i) => ({
        phone: `perfquery${i.toString().padStart(6, '0')}`,
        dnd_status: i % 3 === 0 ? 'DND' as const : i % 3 === 1 ? 'Non-DND' as const : 'Error' as const
      }));

      await persistenceService.saveBulkDNDValidations(testData);
    });

    it('should perform paginated queries efficiently', async () => {
      const start = performance.now();
      const result = await persistenceService.getDNDValidations({
        page: 1,
        limit: 50
      });
      const duration = performance.now() - start;

      expect(result.validations).toHaveLength(50);
      expect(result.total).toBeGreaterThan(500);
      expect(duration).toBeLessThan(200); // Should complete within 200ms
      
      dndMonitoringService.recordOperation('getDNDValidations_paginated', duration, true);
    });

    it('should perform filtered queries efficiently', async () => {
      const start = performance.now();
      const result = await persistenceService.getDNDValidations({
        dndStatus: 'DND',
        page: 1,
        limit: 20
      });
      const duration = performance.now() - start;

      expect(result.validations.every(v => v.dndStatus === 'DND')).toBe(true);
      expect(duration).toBeLessThan(150); // Should complete within 150ms
      
      dndMonitoringService.recordOperation('getDNDValidations_filtered', duration, true);
    });

    it('should perform search queries efficiently', async () => {
      const start = performance.now();
      const result = await persistenceService.getDNDValidations({
        search: 'perfquery000',
        page: 1,
        limit: 10
      });
      const duration = performance.now() - start;

      expect(result.validations.length).toBeGreaterThan(0);
      expect(duration).toBeLessThan(300); // Should complete within 300ms
      
      dndMonitoringService.recordOperation('getDNDValidations_search', duration, true);
    });
  });

  describe('Statistics Performance Benchmarks', () => {
    it('should calculate statistics efficiently', async () => {
      const start = performance.now();
      const stats = await persistenceService.getValidationStats();
      const duration = performance.now() - start;

      expect(stats.totalValidations).toBeGreaterThan(0);
      expect(duration).toBeLessThan(100); // Should complete within 100ms
      
      dndMonitoringService.recordOperation('getValidationStats', duration, true);
    });
  });

  describe('Concurrent Operation Benchmarks', () => {
    it('should handle concurrent single operations', async () => {
      const concurrentOps = 10;
      const operations = Array.from({ length: concurrentOps }, (_, i) => 
        persistenceService.saveDNDValidation({
          phone: `perfconc${i.toString().padStart(6, '0')}`,
          dnd_status: 'DND' as const
        })
      );

      const start = performance.now();
      const results = await Promise.all(operations);
      const duration = performance.now() - start;

      expect(results).toHaveLength(concurrentOps);
      expect(duration).toBeLessThan(1000); // Should complete within 1 second
      
      const throughput = concurrentOps / (duration / 1000);
      expect(throughput).toBeGreaterThan(10); // At least 10 concurrent ops/sec
      
      dndMonitoringService.recordOperation('concurrent_operations', duration, true, {
        concurrency: concurrentOps,
        throughput
      });
    });

    it('should handle mixed concurrent operations', async () => {
      // Setup some data first
      await persistenceService.saveDNDValidation({
        phone: 'perfmixed000001',
        dnd_status: 'DND' as const
      });

      const operations = [
        persistenceService.saveDNDValidation({
          phone: 'perfmixed000002',
          dnd_status: 'Non-DND' as const
        }),
        persistenceService.getDNDValidation('perfmixed000001'),
        persistenceService.isRecentlyValidated('perfmixed000001'),
        persistenceService.getValidationStats()
      ];

      const start = performance.now();
      const results = await Promise.all(operations);
      const duration = performance.now() - start;

      expect(results).toHaveLength(4);
      expect(results[1]).not.toBeNull(); // getDNDValidation result
      expect(results[2]).toBe(true); // isRecentlyValidated result
      expect(duration).toBeLessThan(500); // Should complete within 500ms
      
      dndMonitoringService.recordOperation('mixed_concurrent_operations', duration, true);
    });
  });

  describe('Memory Usage Benchmarks', () => {
    it('should not leak memory during bulk operations', async () => {
      const initialMemory = process.memoryUsage().heapUsed;
      
      // Perform multiple bulk operations
      for (let batch = 0; batch < 5; batch++) {
        const results = Array.from({ length: 100 }, (_, i) => ({
          phone: `perfmem${batch}${i.toString().padStart(4, '0')}`,
          dnd_status: 'DND' as const
        }));
        
        await persistenceService.saveBulkDNDValidations(results);
        
        // Force garbage collection if available
        if (global.gc) {
          global.gc();
        }
      }
      
      const finalMemory = process.memoryUsage().heapUsed;
      const memoryIncrease = finalMemory - initialMemory;
      const memoryIncreasePercent = (memoryIncrease / initialMemory) * 100;
      
      // Memory increase should be reasonable (less than 50% increase)
      expect(memoryIncreasePercent).toBeLessThan(50);
      
      console.log(`Memory usage: ${initialMemory} -> ${finalMemory} (${memoryIncreasePercent.toFixed(2)}% increase)`);
    });
  });

  describe('Database Connection Performance', () => {
    it('should efficiently manage database connections', async () => {
      const connectionsBefore = (await db.query('SELECT count(*) FROM pg_stat_activity')).rows[0].count;
      
      // Perform multiple operations that require database connections
      const operations = Array.from({ length: 20 }, (_, i) => 
        persistenceService.getDNDValidation(`nonexistent${i}`)
      );
      
      await Promise.all(operations);
      
      const connectionsAfter = (await db.query('SELECT count(*) FROM pg_stat_activity')).rows[0].count;
      
      // Connection count should not increase significantly
      expect(parseInt(connectionsAfter) - parseInt(connectionsBefore)).toBeLessThan(5);
    });
  });

  describe('Performance Regression Tests', () => {
    it('should maintain performance standards over time', async () => {
      const benchmarkResults = {
        singleSave: 0,
        singleRetrieve: 0,
        bulkSave100: 0,
        paginatedQuery: 0
      };

      // Single save benchmark
      let start = performance.now();
      await persistenceService.saveDNDValidation({
        phone: 'perfregression001',
        dnd_status: 'DND' as const
      });
      benchmarkResults.singleSave = performance.now() - start;

      // Single retrieve benchmark
      start = performance.now();
      await persistenceService.getDNDValidation('perfregression001');
      benchmarkResults.singleRetrieve = performance.now() - start;

      // Bulk save benchmark
      const bulkData = Array.from({ length: 100 }, (_, i) => ({
        phone: `perfregression${i.toString().padStart(6, '0')}`,
        dnd_status: 'Non-DND' as const
      }));
      
      start = performance.now();
      await persistenceService.saveBulkDNDValidations(bulkData);
      benchmarkResults.bulkSave100 = performance.now() - start;

      // Paginated query benchmark
      start = performance.now();
      await persistenceService.getDNDValidations({ page: 1, limit: 50 });
      benchmarkResults.paginatedQuery = performance.now() - start;

      // Assert performance standards
      expect(benchmarkResults.singleSave).toBeLessThan(100);
      expect(benchmarkResults.singleRetrieve).toBeLessThan(50);
      expect(benchmarkResults.bulkSave100).toBeLessThan(2000);
      expect(benchmarkResults.paginatedQuery).toBeLessThan(200);

      console.log('Performance Benchmark Results:', benchmarkResults);
    });
  });
});
