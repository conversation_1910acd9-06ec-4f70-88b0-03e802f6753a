#!/usr/bin/env tsx

/**
 * Test ElevenLabs Greeting Functionality
 * 
 * This script tests the ElevenLabs TTS integration for generating
 * greeting messages in the AI telecalling system.
 */

import { AudioProcessor } from './src/services/streaming/audio-processor';
import { config } from './src/config/env';
import { writeFileSync } from 'fs';
import { join } from 'path';

async function testElevenLabsGreeting() {
  console.log('🎙️ Testing ElevenLabs Greeting Functionality');
  console.log('=============================================');
  
  // Check if ElevenLabs API key is configured
  const elevenlabsApiKey = config.elevenlabs?.apiKey || process.env.ELEVENLABS_API_KEY;
  
  if (!elevenlabsApiKey) {
    console.log('⚠️ ElevenLabs API key not found. Testing in mock mode...');
  } else {
    console.log(`✅ ElevenLabs API key configured: ${elevenlabsApiKey.substring(0, 8)}...`);
  }
  
  // Initialize AudioProcessor with ElevenLabs configuration
  const audioProcessor = new AudioProcessor({
    sttProvider: 'elevenlabs',
    ttsProvider: 'elevenlabs',
    elevenlabsApiKey: elevenlabsApiKey,
    voice: 'pNInz6obpgDQGcFmaJgB', // Default ElevenLabs voice
    language: 'en'
  });
  
  console.log('\n📋 Configuration:');
  console.log(`   STT Provider: elevenlabs`);
  console.log(`   TTS Provider: elevenlabs`);
  console.log(`   Voice ID: pNInz6obpgDQGcFmaJgB`);
  console.log(`   Language: en`);
  
  // Test greeting messages
  const greetingMessages = [
    "Hello! This is an AI assistant from TeleAI. How can I help you today?",
    "Good morning! Thank you for your interest in our services. I'm here to assist you.",
    "Hi there! I'm calling from TeleAI to discuss how our AI telecalling solution can benefit your business.",
    "Hello! I hope you're having a great day. I'd like to tell you about our innovative AI calling platform.",
    "Good afternoon! This is an automated call from TeleAI. We have some exciting updates to share with you."
  ];
  
  console.log('\n🎯 Testing Greeting Generation...');
  
  try {
    // Test each greeting message
    for (let i = 0; i < greetingMessages.length; i++) {
      const message = greetingMessages[i];
      console.log(`\n📝 Test ${i + 1}: "${message.substring(0, 50)}..."`);
      
      const startTime = Date.now();
      
      try {
        // Generate audio for the greeting
        const audioBuffer = await audioProcessor.textToSpeech(message);
        const duration = Date.now() - startTime;
        
        console.log(`   ✅ Audio generated successfully`);
        console.log(`   📊 Audio size: ${audioBuffer.length} bytes`);
        console.log(`   ⏱️ Generation time: ${duration}ms`);
        
        // Validate audio buffer
        if (audioBuffer.length === 0) {
          console.log(`   ⚠️ Warning: Empty audio buffer`);
        } else if (audioBuffer.length < 1000) {
          console.log(`   ⚠️ Warning: Audio buffer seems too small`);
        }
        
        // Save audio file for manual testing (optional)
        const filename = `greeting_test_${i + 1}.wav`;
        const filepath = join(process.cwd(), 'temp', filename);
        
        try {
          writeFileSync(filepath, audioBuffer);
          console.log(`   💾 Audio saved to: ${filepath}`);
        } catch (saveError) {
          console.log(`   ⚠️ Could not save audio file: ${saveError instanceof Error ? saveError.message : 'Unknown error'}`);
        }
        
        // Test audio chunk validation for Exotel compatibility
        const isValidChunk = audioProcessor.validateChunkSize(audioBuffer);
        console.log(`   🔍 Exotel compatibility: ${isValidChunk ? '✅ Valid' : '⚠️ Needs adjustment'}`);
        
        if (!isValidChunk) {
          const adjustedBuffer = audioProcessor.prepareAudioForExotel(audioBuffer);
          console.log(`   🔧 Adjusted buffer size: ${adjustedBuffer.length} bytes`);
        }
        
      } catch (error) {
        console.log(`   ❌ Error generating audio: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
      
      // Add delay between requests to avoid rate limiting
      if (i < greetingMessages.length - 1) {
        console.log(`   ⏳ Waiting 2 seconds before next test...`);
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    }
    
    // Test health check
    console.log('\n🏥 Testing AudioProcessor Health Check...');
    const isHealthy = await audioProcessor.healthCheck();
    console.log(`   Health Status: ${isHealthy ? '✅ Healthy' : '❌ Unhealthy'}`);
    
    // Test STT functionality with mock audio
    console.log('\n🎤 Testing Speech-to-Text (Mock Audio)...');
    const mockAudioBuffer = Buffer.alloc(16000); // 1 second of silence at 16kHz
    const transcription = await audioProcessor.speechToText(mockAudioBuffer);
    
    if (transcription) {
      console.log(`   ✅ STT Result: "${transcription.text}"`);
      console.log(`   📊 Confidence: ${transcription.confidence}`);
      console.log(`   🌐 Language: ${transcription.language}`);
    } else {
      console.log(`   ⚠️ No transcription result (expected for silence)`);
    }
    
    console.log('\n🎉 ElevenLabs Greeting Test Completed!');
    
    // Summary
    console.log('\n📊 Test Summary:');
    console.log(`   ✅ Tested ${greetingMessages.length} greeting messages`);
    console.log(`   ✅ Audio generation working`);
    console.log(`   ✅ Exotel compatibility checked`);
    console.log(`   ✅ Health check performed`);
    console.log(`   ✅ STT functionality tested`);
    
    if (!elevenlabsApiKey) {
      console.log('\n💡 Note: Tests ran in mock mode. Configure ELEVENLABS_API_KEY for real API testing.');
    }
    
  } catch (error) {
    console.error('\n❌ Test failed:', error);
    process.exit(1);
  }
}

// Run the test
if (require.main === module) {
  testElevenLabsGreeting()
    .then(() => {
      console.log('\n✅ All tests completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test suite failed:', error);
      process.exit(1);
    });
}

export { testElevenLabsGreeting };
