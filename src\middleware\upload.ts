import multer from 'multer';
import { Request } from 'express';
import { config } from '../config';
import logger from '../utils/logger';

// File filter for CSV uploads
const csvFileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  logger.info(`File upload attempt: ${file.originalname}, mimetype: ${file.mimetype}`);
  
  // Check file extension
  const allowedExtensions = ['.csv'];
  const fileExtension = file.originalname.toLowerCase().substring(file.originalname.lastIndexOf('.'));
  
  if (!allowedExtensions.includes(fileExtension)) {
    logger.warn(`Rejected file with invalid extension: ${fileExtension}`);
    return cb(new Error('Only CSV files are allowed'));
  }
  
  // Check MIME type (note: CSV files can have various MIME types)
  const allowedMimeTypes = [
    'text/csv',
    'application/csv',
    'text/plain',
    'application/vnd.ms-excel',
    'text/comma-separated-values'
  ];
  
  if (!allowedMimeTypes.includes(file.mimetype)) {
    logger.warn(`Rejected file with invalid MIME type: ${file.mimetype}`);
    return cb(new Error('Invalid file type. Please upload a CSV file'));
  }
  
  logger.info(`File validation passed: ${file.originalname}`);
  cb(null, true);
};

// Multer configuration for CSV uploads
const csvUploadConfig = multer({
  storage: multer.memoryStorage(), // Store in memory for processing
  limits: {
    fileSize: config.dnd.maxFileSizeMB * 1024 * 1024, // Convert MB to bytes
    files: 1, // Only allow one file at a time
    fields: 5, // Limit number of form fields
    fieldSize: 1024 * 1024 // 1MB field size limit
  },
  fileFilter: csvFileFilter
});

// Error handler for multer errors
export const handleUploadError = (error: any, req: Request, res: any, next: any) => {
  if (error instanceof multer.MulterError) {
    logger.error('Multer upload error:', error);
    
    switch (error.code) {
      case 'LIMIT_FILE_SIZE':
        return res.status(400).json({
          success: false,
          error: `File too large. Maximum size allowed is ${config.dnd.maxFileSizeMB}MB`
        });
      case 'LIMIT_FILE_COUNT':
        return res.status(400).json({
          success: false,
          error: 'Only one file can be uploaded at a time'
        });
      case 'LIMIT_UNEXPECTED_FILE':
        return res.status(400).json({
          success: false,
          error: 'Unexpected file field. Use "csvFile" as the field name'
        });
      default:
        return res.status(400).json({
          success: false,
          error: `Upload error: ${error.message}`
        });
    }
  }
  
  // Handle custom file filter errors
  if (error.message) {
    logger.error('File filter error:', error.message);
    return res.status(400).json({
      success: false,
      error: error.message
    });
  }
  
  // Pass other errors to the next error handler
  next(error);
};

// Middleware to validate uploaded file
export const validateUploadedFile = (req: Request, res: any, next: any) => {
  if (!req.file) {
    logger.warn('No file uploaded in request');
    return res.status(400).json({
      success: false,
      error: 'No file uploaded. Please provide a CSV file using the "csvFile" field'
    });
  }
  
  if (!req.file.buffer || req.file.buffer.length === 0) {
    logger.warn('Uploaded file is empty');
    return res.status(400).json({
      success: false,
      error: 'Uploaded file is empty'
    });
  }
  
  // Check if file content looks like CSV (basic check)
  const fileContent = req.file.buffer.toString('utf8', 0, Math.min(1000, req.file.buffer.length));
  const hasCommas = fileContent.includes(',');
  const hasNewlines = fileContent.includes('\n') || fileContent.includes('\r');
  
  if (!hasCommas || !hasNewlines) {
    logger.warn('File does not appear to be a valid CSV format');
    return res.status(400).json({
      success: false,
      error: 'File does not appear to be a valid CSV format'
    });
  }
  
  logger.info(`File validation successful: ${req.file.originalname}, size: ${req.file.size} bytes`);
  next();
};

// Main upload middleware for CSV files
export const uploadCSV = csvUploadConfig.single('csvFile');

// Combined middleware for easy use
export const csvUploadMiddleware = [
  uploadCSV,
  handleUploadError,
  validateUploadedFile
];

export default {
  uploadCSV,
  handleUploadError,
  validateUploadedFile,
  csvUploadMiddleware
};
