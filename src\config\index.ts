import dotenv from 'dotenv';

// Load environment variables
dotenv.config();

export const config = {
  port: parseInt(process.env.PORT || '3000', 10),
  nodeEnv: process.env.NODE_ENV || 'development',

  database: {
    url: process.env.DATABASE_URL || 'postgresql://postgres:password@localhost:5432/ai_telecalling_mvp',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432', 10),
    name: process.env.DB_NAME || 'ai_telecalling_mvp',
    user: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || 'password'
  },

  jwt: {
    secret: process.env.JWT_SECRET || 'dev-secret-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '24h'
  },

  twilio: {
    accountSid: process.env.TWILIO_ACCOUNT_SID || 'test_account_sid',
    authToken: process.env.TWILIO_AUTH_TOKEN || 'test_auth_token',
    phoneNumber: process.env.TWILIO_PHONE_NUMBER || '+**********'
  },

  litellm: {
    baseUrl: process.env.LITELLM_BASE_URL || 'http://localhost:8000',
    apiKey: process.env.LITELLM_API_KEY || 'test-key'
  },

  dnd: {
    // msgclub API configuration
    apiBaseUrl: process.env.DND_API_BASE_URL || 'http://msg.msgclub.net',
    apiEndpoint: process.env.DND_API_ENDPOINT || '/rest/services/sendSMS/getNdncNonNdncNumbers',
    authKey: process.env.DND_AUTH_KEY || 'd5ce6115694915cd9fa8df8536d330',
    timeout: parseInt(process.env.DND_API_TIMEOUT || '30000', 10),

    // Legacy endpoints (kept for backward compatibility)
    bulkEndpoint: process.env.DND_BULK_ENDPOINT || '/bulk-check',
    singleEndpoint: process.env.DND_SINGLE_ENDPOINT || '/check',
    apiToken: process.env.DND_API_TOKEN || 'test-dnd-token',

    // Processing configuration
    maxConcurrentRequests: parseInt(process.env.MAX_CONCURRENT_REQUESTS || '5', 10), // Reduced for msgclub
    retryAttempts: parseInt(process.env.RETRY_ATTEMPTS || '3', 10),
    retryDelayMs: parseInt(process.env.RETRY_DELAY_MS || '2000', 10),
    maxBatchSize: parseInt(process.env.DND_MAX_BATCH_SIZE || '100', 10), // msgclub limit

    // File processing limits
    maxFileSizeMB: parseInt(process.env.MAX_FILE_SIZE_MB || '50', 10),
    maxRecordsLimit: parseInt(process.env.MAX_RECORDS_LIMIT || '100000', 10)
  }
};



export default config;
