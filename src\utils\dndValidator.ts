/**
 * @fileoverview DND Validation and Sanitization Utilities
 * @description Comprehensive input validation and sanitization for DND persistence operations
 * <AUTHOR> System
 * @version 1.0.0
 */

import { IDNDValidator, ValidationResult } from '../types';
import { DND_VALIDATION_CONSTANTS } from '../config/dndPersistence';
import { 
  InvalidPhoneNumberError, 
  InvalidDNDStatusError, 
  InvalidMetadataError,
  DNDErrorFactory 
} from './dndPersistenceErrors';
import logger from './logger';

/**
 * Comprehensive validator for DND persistence operations
 * @implements {IDNDValidator}
 */
export class DNDValidator implements IDNDValidator {
  private readonly phonePattern = DND_VALIDATION_CONSTANTS.PHONE_NUMBER.INDIAN_MOBILE_PATTERN;
  private readonly internationalPattern = DND_VALIDATION_CONSTANTS.PHONE_NUMBER.INTERNATIONAL_PATTERN;
  private readonly validDNDStatuses = Object.values(DND_VALIDATION_CONSTANTS.DND_STATUS);

  /**
   * Validates phone number format and structure
   * @param {string} phoneNumber - Phone number to validate
   * @returns {ValidationResult} Validation result with sanitized value
   * @throws {InvalidPhoneNumberError} When phone number is invalid
   */
  validatePhoneNumber(phoneNumber: string): ValidationResult {
    try {
      // Basic type and null checks
      if (!phoneNumber || typeof phoneNumber !== 'string') {
        return {
          isValid: false,
          error: 'Phone number must be a non-empty string'
        };
      }

      // Remove whitespace and normalize
      const cleaned = phoneNumber.trim();
      
      if (cleaned.length === 0) {
        return {
          isValid: false,
          error: 'Phone number cannot be empty'
        };
      }

      // Length validation
      if (cleaned.length < DND_VALIDATION_CONSTANTS.PHONE_NUMBER.MIN_LENGTH ||
          cleaned.length > DND_VALIDATION_CONSTANTS.PHONE_NUMBER.MAX_LENGTH) {
        return {
          isValid: false,
          error: `Phone number length must be between ${DND_VALIDATION_CONSTANTS.PHONE_NUMBER.MIN_LENGTH} and ${DND_VALIDATION_CONSTANTS.PHONE_NUMBER.MAX_LENGTH} characters`
        };
      }

      // Sanitize and normalize
      const sanitized = this.sanitizePhoneNumber(cleaned);
      
      // Validate format
      if (!this.phonePattern.test(sanitized) && !this.internationalPattern.test(sanitized)) {
        return {
          isValid: false,
          error: 'Invalid phone number format. Must be a valid Indian mobile number or international format'
        };
      }

      return {
        isValid: true,
        sanitized
      };
    } catch (error) {
      logger.error('Phone number validation error:', error);
      return {
        isValid: false,
        error: 'Phone number validation failed due to internal error'
      };
    }
  }

  /**
   * Validates DND status value
   * @param {string} status - DND status to validate
   * @returns {ValidationResult} Validation result
   * @throws {InvalidDNDStatusError} When status is invalid
   */
  validateDNDStatus(status: string): ValidationResult {
    try {
      if (!status || typeof status !== 'string') {
        return {
          isValid: false,
          error: 'DND status must be a non-empty string'
        };
      }

      const trimmedStatus = status.trim();
      
      if (!this.validDNDStatuses.includes(trimmedStatus as any)) {
        return {
          isValid: false,
          error: `Invalid DND status. Valid values: ${this.validDNDStatuses.join(', ')}`
        };
      }

      return {
        isValid: true,
        sanitized: trimmedStatus
      };
    } catch (error) {
      logger.error('DND status validation error:', error);
      return {
        isValid: false,
        error: 'DND status validation failed due to internal error'
      };
    }
  }

  /**
   * Validates and sanitizes metadata object
   * @param {any} metadata - Metadata to validate
   * @returns {ValidationResult} Validation result with sanitized metadata
   * @throws {InvalidMetadataError} When metadata is invalid
   */
  validateMetadata(metadata: any): ValidationResult {
    try {
      // Allow null or undefined metadata
      if (metadata === null || metadata === undefined) {
        return {
          isValid: true,
          sanitized: {}
        };
      }

      // Must be an object
      if (typeof metadata !== 'object' || Array.isArray(metadata)) {
        return {
          isValid: false,
          error: 'Metadata must be an object'
        };
      }

      // Check size constraints
      const metadataString = JSON.stringify(metadata);
      const sizeBytes = Buffer.byteLength(metadataString, 'utf8');
      
      if (sizeBytes > DND_VALIDATION_CONSTANTS.METADATA.MAX_SIZE_BYTES) {
        return {
          isValid: false,
          error: `Metadata size exceeds maximum allowed size of ${DND_VALIDATION_CONSTANTS.METADATA.MAX_SIZE_BYTES} bytes`
        };
      }

      // Check number of keys
      const keyCount = Object.keys(metadata).length;
      if (keyCount > DND_VALIDATION_CONSTANTS.METADATA.MAX_KEYS) {
        return {
          isValid: false,
          error: `Metadata cannot have more than ${DND_VALIDATION_CONSTANTS.METADATA.MAX_KEYS} keys`
        };
      }

      // Sanitize metadata
      const sanitized = this.sanitizeMetadata(metadata);

      return {
        isValid: true,
        sanitized
      };
    } catch (error) {
      logger.error('Metadata validation error:', error);
      return {
        isValid: false,
        error: 'Metadata validation failed due to internal error'
      };
    }
  }

  /**
   * Sanitizes phone number by removing non-digit characters and normalizing format
   * @param {string} phoneNumber - Phone number to sanitize
   * @returns {string} Sanitized phone number
   */
  sanitizePhoneNumber(phoneNumber: string): string {
    try {
      // Remove all non-digit characters except +
      let cleaned = phoneNumber.replace(/[^\d+]/g, '');
      
      // Handle different input formats
      if (cleaned.startsWith('+91') && cleaned.length === 13) {
        // Remove country code +91 for Indian numbers
        cleaned = cleaned.substring(3);
      } else if (cleaned.startsWith('91') && cleaned.length === 12) {
        // Remove country code 91 for Indian numbers
        cleaned = cleaned.substring(2);
      } else if (cleaned.startsWith('0') && cleaned.length === 11) {
        // Remove leading 0 from Indian numbers
        cleaned = cleaned.substring(1);
      }
      
      // Validate and return normalized format
      if (cleaned.length === 10 && this.phonePattern.test(cleaned)) {
        return cleaned; // Return 10-digit Indian mobile number
      }
      
      // Return as-is for international numbers or if validation fails
      return phoneNumber.replace(/[^\d+]/g, '');
    } catch (error) {
      logger.error('Phone number sanitization error:', error);
      return phoneNumber;
    }
  }

  /**
   * Sanitizes metadata by removing dangerous content and normalizing structure
   * @param {any} metadata - Metadata to sanitize
   * @returns {Record<string, any>} Sanitized metadata
   */
  sanitizeMetadata(metadata: any): Record<string, any> {
    try {
      if (!metadata || typeof metadata !== 'object') {
        return {};
      }

      const sanitized: Record<string, any> = {};
      const dangerousKeys = ['__proto__', 'constructor', 'prototype'];
      
      for (const [key, value] of Object.entries(metadata)) {
        // Skip dangerous keys
        if (dangerousKeys.includes(key.toLowerCase())) {
          continue;
        }

        // Sanitize key
        const sanitizedKey = this.sanitizeString(key);
        if (!sanitizedKey) {
          continue;
        }

        // Sanitize value based on type
        if (typeof value === 'string') {
          sanitized[sanitizedKey] = this.sanitizeString(value);
        } else if (typeof value === 'number' && isFinite(value)) {
          sanitized[sanitizedKey] = value;
        } else if (typeof value === 'boolean') {
          sanitized[sanitizedKey] = value;
        } else if (value instanceof Date) {
          sanitized[sanitizedKey] = value.toISOString();
        } else if (Array.isArray(value)) {
          sanitized[sanitizedKey] = value.slice(0, 10).map(item => 
            typeof item === 'string' ? this.sanitizeString(item) : item
          );
        } else if (value && typeof value === 'object') {
          // Recursively sanitize nested objects (max depth 2)
          sanitized[sanitizedKey] = this.sanitizeNestedObject(value, 1);
        }
      }

      return sanitized;
    } catch (error) {
      logger.error('Metadata sanitization error:', error);
      return {};
    }
  }

  /**
   * Sanitizes string values by removing potentially dangerous content
   * @private
   * @param {string} str - String to sanitize
   * @returns {string} Sanitized string
   */
  private sanitizeString(str: string): string {
    if (typeof str !== 'string') {
      return '';
    }

    return str
      .trim()
      .slice(0, 1000) // Limit length
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove script tags
      .replace(/javascript:/gi, '') // Remove javascript: protocol
      .replace(/on\w+\s*=/gi, ''); // Remove event handlers
  }

  /**
   * Sanitizes nested objects with depth limit
   * @private
   * @param {any} obj - Object to sanitize
   * @param {number} depth - Current depth
   * @returns {Record<string, any>} Sanitized object
   */
  private sanitizeNestedObject(obj: any, depth: number): Record<string, any> {
    if (depth > 2 || !obj || typeof obj !== 'object') {
      return {};
    }

    const sanitized: Record<string, any> = {};
    
    for (const [key, value] of Object.entries(obj)) {
      const sanitizedKey = this.sanitizeString(key);
      if (!sanitizedKey) continue;

      if (typeof value === 'string') {
        sanitized[sanitizedKey] = this.sanitizeString(value);
      } else if (typeof value === 'number' && isFinite(value)) {
        sanitized[sanitizedKey] = value;
      } else if (typeof value === 'boolean') {
        sanitized[sanitizedKey] = value;
      }
    }

    return sanitized;
  }

  /**
   * Validates bulk operation size
   * @param {number} size - Size of bulk operation
   * @param {number} maxSize - Maximum allowed size
   * @returns {ValidationResult} Validation result
   */
  validateBulkSize(size: number, maxSize: number): ValidationResult {
    if (typeof size !== 'number' || size < 0) {
      return {
        isValid: false,
        error: 'Bulk size must be a non-negative number'
      };
    }

    if (size === 0) {
      return {
        isValid: false,
        error: 'Bulk operation cannot be empty'
      };
    }

    if (size > maxSize) {
      return {
        isValid: false,
        error: `Bulk operation size ${size} exceeds maximum allowed size ${maxSize}`
      };
    }

    return {
      isValid: true
    };
  }

  /**
   * Validates pagination parameters
   * @param {number} page - Page number
   * @param {number} limit - Items per page
   * @returns {ValidationResult} Validation result
   */
  validatePagination(page: number, limit: number): ValidationResult {
    if (typeof page !== 'number' || page < 1) {
      return {
        isValid: false,
        error: 'Page must be a positive number starting from 1'
      };
    }

    if (typeof limit !== 'number' || limit < 1 || limit > 1000) {
      return {
        isValid: false,
        error: 'Limit must be a positive number between 1 and 1000'
      };
    }

    return {
      isValid: true,
      sanitized: { page: Math.floor(page), limit: Math.floor(limit) }
    };
  }
}

/**
 * Singleton instance of DNDValidator
 */
export const dndValidator = new DNDValidator();

/**
 * Utility function to validate and throw appropriate errors
 * @param {ValidationResult} result - Validation result
 * @param {string} field - Field name for error context
 * @throws {DNDValidationError} When validation fails
 */
export function validateAndThrow(result: ValidationResult, field: string): void {
  if (!result.isValid) {
    throw DNDErrorFactory.fromValidationFailure(field, undefined, result.error || 'Validation failed');
  }
}
