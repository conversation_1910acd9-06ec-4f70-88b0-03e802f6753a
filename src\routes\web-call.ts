import { Router } from 'express';
import multer from 'multer';
import { ConversationEngine, ConversationConfig } from '../services/streaming/conversation-engine';
import { AudioProcessor, AudioProcessorConfig } from '../services/streaming/audio-processor';
import { LiteLLMService } from '../services/ai/litellm-service';
import { Lead } from '../types';
import path from 'path';
import fs from 'fs';

const router = Router();

// Configure multer for audio file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 10 * 1024 * 1024, // 10MB limit
  },
  fileFilter: (req, file, cb) => {
    if (file.mimetype.startsWith('audio/')) {
      cb(null, true);
    } else {
      cb(new Error('Only audio files are allowed'));
    }
  }
});

// Store active web call sessions
interface WebCallSession {
  sessionId: string;
  conversationEngine: ConversationEngine;
  audioProcessor: AudioProcessor;
  startTime: Date;
  messageCount: number;
}

const activeSessions = new Map<string, WebCallSession>();

/**
 * POST /api/v1/web-call/greeting
 * Get initial AI greeting for web call
 */
router.post('/greeting', async (req, res) => {
  try {
    const { sessionId } = req.body;
    
    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      });
    }

    console.log('🎙️ Starting web call session:', sessionId);

    // Create demo lead for web call
    const demoLead: Lead = {
      id: 0,
      name: 'Web Demo User',
      phone: '******-DEMO',
      email: '<EMAIL>',
      status: 'new',
      notes: 'Web call demo session',
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // Initialize LiteLLM service
    const litellm = new LiteLLMService({
      baseUrl: process.env.LITELLM_BASE_URL || 'http://localhost:4000',
      apiKey: process.env.LITELLM_API_KEY || 'default-api-key',
      model: process.env.LITELLM_MODEL || 'deepseek-chat',
      timeout: 10000 // Reduced timeout for faster web calls
    });

    // Initialize conversation engine
    const conversationConfig: ConversationConfig = {
      maxHistoryLength: 6, // Reduced for faster processing
      defaultPersonality: 'professional',
      responseTimeout: 10000, // Reduced timeout for faster responses
      enableIntentDetection: false, // Disabled for faster processing in web demo
      enableDataExtraction: false // Disabled for faster processing in web demo
    };
    const conversationEngine = new ConversationEngine(litellm, conversationConfig);
    await conversationEngine.initializeSession(sessionId, demoLead.id);

    // Initialize audio processor with ElevenLabs
    const audioConfig: AudioProcessorConfig = {
      sttProvider: process.env.STT_PROVIDER as 'openai' | 'deepgram' | 'elevenlabs' | 'mock' || 'elevenlabs',
      ttsProvider: process.env.TTS_PROVIDER as 'openai' | 'elevenlabs' | 'mock' || 'elevenlabs',
      openaiApiKey: process.env.OPENAI_API_KEY,
      deepgramApiKey: process.env.DEEPGRAM_API_KEY,
      elevenlabsApiKey: process.env.ELEVENLABS_API_KEY,
      voice: process.env.TTS_VOICE || process.env.ELEVENLABS_VOICE_ID || 'pNInz6obpgDQGcFmaJgB', // ElevenLabs voice
      language: process.env.STT_LANGUAGE || 'en'
    };
    const audioProcessor = new AudioProcessor(audioConfig);

    console.log('🎙️ Web call audio configuration:', {
      sttProvider: audioConfig.sttProvider,
      ttsProvider: audioConfig.ttsProvider,
      voice: audioConfig.voice,
      hasElevenlabsKey: !!audioConfig.elevenlabsApiKey
    });

    // Generate initial greeting
    const greeting = await conversationEngine.generatePersonalizedGreeting(demoLead);
    console.log('Invoking textToSpeech with greeting:', greeting);
    
    // Convert greeting to audio with error logging
    let audioBuffer;
    try {
      audioBuffer = await audioProcessor.textToSpeech(greeting);
      console.log('🎵 Generated audio buffer size:', audioBuffer.length, 'bytes');
    } catch (ttsError) {
      console.error('❌ Error during textToSpeech call:', ttsError);
      throw ttsError;
    }

    // Save audio file temporarily
    const audioFileName = `greeting-${sessionId}-${Date.now()}.wav`;
    const audioPath = path.join(process.cwd(), 'temp', audioFileName);

    // Ensure temp directory exists
    const tempDir = path.dirname(audioPath);
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true });
      console.log('📁 Created temp directory:', tempDir);
    }

    fs.writeFileSync(audioPath, audioBuffer);
    console.log('💾 Audio file saved:', audioPath);

    // Verify file was created
    if (fs.existsSync(audioPath)) {
      const stats = fs.statSync(audioPath);
      console.log('✅ Audio file verified - size:', stats.size, 'bytes');
    } else {
      console.log('❌ Audio file was not created!');
    }

    // Store session
    activeSessions.set(sessionId, {
      sessionId,
      conversationEngine,
      audioProcessor,
      startTime: new Date(),
      messageCount: 1
    });

    console.log('✅ Web call greeting generated:', greeting);

    return res.json({
      success: true,
      greeting,
      audioUrl: `/api/v1/web-call/audio/${audioFileName}`,
      sessionId
    });

  } catch (error) {
    console.error('❌ Error generating web call greeting:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to generate greeting'
    });
  }
});

/**
 * POST /api/v1/web-call/process-audio
 * Process user audio and generate AI response
 */
router.post('/process-audio', upload.single('audio'), async (req, res) => {
  try {
    const { sessionId } = req.body;
    const audioFile = req.file;

    if (!sessionId || !audioFile) {
      return res.status(400).json({
        success: false,
        error: 'Session ID and audio file are required'
      });
    }

    const session = activeSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }

    console.log('🎤 Processing audio for session:', sessionId);

    // Convert audio to text using STT
    const transcriptionResult = await session.audioProcessor.speechToText(audioFile.buffer);

    if (!transcriptionResult || !transcriptionResult.text || transcriptionResult.text.trim().length === 0) {
      return res.json({
        success: true,
        transcription: '',
        message: 'No speech detected'
      });
    }

    const transcription = transcriptionResult.text;
    console.log('📝 Transcription:', transcription);

    // Generate AI response
    const aiResponse = await session.conversationEngine.processInput(sessionId, transcription);

    if (!aiResponse) {
      return res.json({
        success: true,
        transcription,
        confidence: transcriptionResult.confidence || 0.95,
        message: 'No AI response generated'
      });
    }

    // Convert AI response to audio
    const responseAudioBuffer = await session.audioProcessor.textToSpeech(aiResponse);
    
    // Save response audio file
    const responseAudioFileName = `response-${sessionId}-${Date.now()}.wav`;
    const responseAudioPath = path.join(process.cwd(), 'temp', responseAudioFileName);
    fs.writeFileSync(responseAudioPath, responseAudioBuffer);

    // Update session
    session.messageCount += 2; // User message + AI response

    console.log('🤖 AI Response:', aiResponse);

    return res.json({
      success: true,
      transcription,
      confidence: transcriptionResult.confidence || 0.95,
      aiResponse: aiResponse,
      audioUrl: `/api/v1/web-call/audio/${responseAudioFileName}`,
      messageCount: session.messageCount
    });

  } catch (error) {
    console.error('❌ Error processing audio:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to process audio'
    });
  }
});

/**
 * GET /api/v1/web-call/audio/:filename
 * Serve audio files
 */
router.get('/audio/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const audioPath = path.join(process.cwd(), 'temp', filename);

    console.log('🎵 Audio file requested:', filename);
    console.log('🎵 Audio file path:', audioPath);

    if (!fs.existsSync(audioPath)) {
      console.log('❌ Audio file not found:', audioPath);
      return res.status(404).json({
        success: false,
        error: 'Audio file not found'
      });
    }

    const stats = fs.statSync(audioPath);
    console.log('🎵 Audio file size:', stats.size, 'bytes');

    res.setHeader('Content-Type', 'audio/wav');
    res.setHeader('Cache-Control', 'no-cache');

    const audioStream = fs.createReadStream(audioPath);
    audioStream.pipe(res);

    console.log('🎵 Audio file served successfully:', filename);

    // Clean up file after serving (with delay)
    setTimeout(() => {
      if (fs.existsSync(audioPath)) {
        fs.unlinkSync(audioPath);
        console.log('🗑️ Cleaned up audio file:', filename);
      }
    }, 30000); // 30 seconds delay

    return; // Explicit return for TypeScript

  } catch (error) {
    console.error('❌ Error serving audio file:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to serve audio file'
    });
  }
});

/**
 * POST /api/v1/web-call/end
 * End web call session
 */
router.post('/end', async (req, res) => {
  try {
    const { sessionId } = req.body;

    if (!sessionId) {
      return res.status(400).json({
        success: false,
        error: 'Session ID is required'
      });
    }

    const session = activeSessions.get(sessionId);
    if (!session) {
      return res.status(404).json({
        success: false,
        error: 'Session not found'
      });
    }

    // Calculate session duration
    const duration = Math.floor((new Date().getTime() - session.startTime.getTime()) / 1000);

    // Clean up session
    activeSessions.delete(sessionId);

    console.log('📞 Web call session ended:', sessionId, `Duration: ${duration}s`);

    return res.json({
      success: true,
      sessionId,
      duration,
      messageCount: session.messageCount
    });

  } catch (error) {
    console.error('❌ Error ending web call session:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to end session'
    });
  }
});

/**
 * GET /api/v1/web-call/sessions
 * Get active web call sessions
 */
router.get('/sessions', (_req, res) => {
  try {
    const sessions = Array.from(activeSessions.values()).map(session => ({
      sessionId: session.sessionId,
      startTime: session.startTime,
      messageCount: session.messageCount,
      duration: Math.floor((new Date().getTime() - session.startTime.getTime()) / 1000)
    }));

    return res.json({
      success: true,
      sessions,
      count: sessions.length
    });

  } catch (error) {
    console.error('❌ Error getting web call sessions:', error);
    return res.status(500).json({
      success: false,
      error: 'Failed to get sessions'
    });
  }
});

export default router;
