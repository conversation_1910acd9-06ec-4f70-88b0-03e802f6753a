import React, { useState, useMemo } from 'react';
import { 
  Search, 
  Filter, 
  Download, 
  RefreshCw, 
  ArrowLeft, 
  CheckSquare, 
  Square,
  BarChart3,
  Clock,
  FileText,
  Users
} from 'lucide-react';
import { Button } from '../ui/Button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/Card';
import { DndResultsTable } from './DndResultsTable';
import { DndStatusBadge } from './DndStatusBadge';
import { DNDRecord, DNDResultsState, DNDUIState } from '../../types';
import { exportFilteredData, formatFileSize } from '../../utils/csvProcessor';

interface DndResultsManagerProps {
  resultsState: DNDResultsState;
  uiState: DNDUIState;
  filteredData: DNDRecord[];
  paginatedData: DNDRecord[];
  totalPages: number;
  onUpdateFilters: (filters: Partial<DNDUIState['filters']>) => void;
  onUpdateSort: (sortBy: keyof DNDRecord, sortOrder?: 'asc' | 'desc') => void;
  onUpdatePagination: (page: number, pageSize?: number) => void;
  onStartOver: () => void;
  onBackToUpload: () => void;
}

export const DndResultsManager: React.FC<DndResultsManagerProps> = ({
  resultsState,
  uiState,
  filteredData,
  paginatedData,
  totalPages,
  onUpdateFilters,
  onUpdateSort,
  onUpdatePagination,
  onStartOver,
  onBackToUpload,
}) => {
  const [selectedRows, setSelectedRows] = useState<Set<number>>(new Set());
  const [showFilters, setShowFilters] = useState(false);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    onUpdateFilters({ searchTerm: e.target.value });
  };

  const handleStatusFilter = (status: 'all' | 'DND' | 'Non-DND' | 'Error') => {
    onUpdateFilters({ dndStatus: status });
  };

  const handleErrorsOnlyToggle = () => {
    onUpdateFilters({ showErrorsOnly: !uiState.filters.showErrorsOnly });
  };

  const handleRowSelect = (index: number, selected: boolean) => {
    const newSelected = new Set(selectedRows);
    if (selected) {
      newSelected.add(index);
    } else {
      newSelected.delete(index);
    }
    setSelectedRows(newSelected);
  };

  const handleSelectAll = (selected: boolean) => {
    if (selected) {
      const allIndexes = new Set(Array.from({ length: paginatedData.length }, (_, i) => i));
      setSelectedRows(allIndexes);
    } else {
      setSelectedRows(new Set());
    }
  };

  const handleExport = (filter: 'all' | 'DND' | 'Non-DND' | 'Error' = 'all') => {
    exportFilteredData(resultsState.data, filter);
  };

  const handleExportSelected = () => {
    const selectedData = Array.from(selectedRows).map(index => paginatedData[index]);
    exportFilteredData(selectedData, 'all', 'selected-records.csv');
  };

  const summaryStats = useMemo(() => {
    if (!resultsState.summary) return null;
    
    const { summary } = resultsState;
    return [
      {
        label: 'Total Records',
        value: summary.totalRecords.toLocaleString(),
        icon: <FileText className="h-5 w-5 text-blue-500" />,
        color: 'text-blue-600',
      },
      {
        label: 'DND Numbers',
        value: summary.dndCount.toLocaleString(),
        icon: <Users className="h-5 w-5 text-red-500" />,
        color: 'text-red-600',
      },
      {
        label: 'Non-DND Numbers',
        value: summary.nonDndCount.toLocaleString(),
        icon: <Users className="h-5 w-5 text-green-500" />,
        color: 'text-green-600',
      },
      {
        label: 'Errors',
        value: summary.errorCount.toLocaleString(),
        icon: <BarChart3 className="h-5 w-5 text-yellow-500" />,
        color: 'text-yellow-600',
      },
    ];
  }, [resultsState.summary]);

  if (resultsState.isLoading) {
    return (
      <div className="flex items-center justify-center py-12">
        <div className="text-center">
          <RefreshCw className="h-8 w-8 animate-spin text-primary-600 mx-auto mb-4" />
          <p className="text-gray-600">Loading results...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Button
            variant="outline"
            onClick={onBackToUpload}
            icon={<ArrowLeft className="h-4 w-4" />}
          >
            Back to Upload
          </Button>
          <Button
            variant="outline"
            onClick={onStartOver}
            icon={<RefreshCw className="h-4 w-4" />}
          >
            Start Over
          </Button>
        </div>

        <div className="flex items-center space-x-2">
          {selectedRows.size > 0 && (
            <Button
              variant="outline"
              onClick={handleExportSelected}
              icon={<Download className="h-4 w-4" />}
            >
              Export Selected ({selectedRows.size})
            </Button>
          )}
          
          <div className="relative">
            <Button
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              icon={<Download className="h-4 w-4" />}
            >
              Export
            </Button>
            
            {showFilters && (
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-10">
                <div className="py-1">
                  <button
                    onClick={() => handleExport('all')}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Export All Results
                  </button>
                  <button
                    onClick={() => handleExport('DND')}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Export DND Only
                  </button>
                  <button
                    onClick={() => handleExport('Non-DND')}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Export Non-DND Only
                  </button>
                  <button
                    onClick={() => handleExport('Error')}
                    className="block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                  >
                    Export Errors Only
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Summary Statistics */}
      {summaryStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {summaryStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    {stat.icon}
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-500">{stat.label}</p>
                    <p className={`text-2xl font-semibold ${stat.color}`}>{stat.value}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Processing Metadata */}
      {resultsState.metadata && (
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between text-sm text-gray-600">
              <div className="flex items-center space-x-4">
                <div className="flex items-center">
                  <FileText className="h-4 w-4 mr-1" />
                  <span>{resultsState.metadata.fileName}</span>
                </div>
                <div className="flex items-center">
                  <span>{formatFileSize(resultsState.metadata.fileSize)}</span>
                </div>
                <div className="flex items-center">
                  <Clock className="h-4 w-4 mr-1" />
                  <span>Processed in {(resultsState.metadata.processingTimeMs / 1000).toFixed(1)}s</span>
                </div>
              </div>
              <div>
                <span>{new Date(resultsState.metadata.timestamp).toLocaleString()}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Filters and Search */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
            <div className="flex items-center space-x-4">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search records..."
                  value={uiState.filters.searchTerm}
                  onChange={handleSearch}
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-primary-500 focus:border-primary-500"
                />
              </div>
              
              <div className="flex items-center space-x-2">
                <span className="text-sm text-gray-500">Filter:</span>
                <div className="flex space-x-1">
                  {(['all', 'DND', 'Non-DND', 'Error'] as const).map((status) => (
                    <button
                      key={status}
                      onClick={() => handleStatusFilter(status)}
                      className={`px-3 py-1 text-xs rounded-full border transition-colors ${
                        uiState.filters.dndStatus === status
                          ? 'bg-primary-100 border-primary-300 text-primary-700'
                          : 'bg-white border-gray-300 text-gray-600 hover:bg-gray-50'
                      }`}
                    >
                      {status === 'all' ? 'All' : status}
                    </button>
                  ))}
                </div>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <button
                onClick={handleErrorsOnlyToggle}
                className="flex items-center space-x-2 text-sm text-gray-600 hover:text-gray-800"
              >
                {uiState.filters.showErrorsOnly ? (
                  <CheckSquare className="h-4 w-4" />
                ) : (
                  <Square className="h-4 w-4" />
                )}
                <span>Errors only</span>
              </button>
              
              <span className="text-sm text-gray-500">
                Showing {filteredData.length.toLocaleString()} of {resultsState.data.length.toLocaleString()} records
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Results Table */}
      <Card>
        <CardContent className="p-0">
          <DndResultsTable
            data={paginatedData}
            uiState={uiState}
            onUpdateSort={onUpdateSort}
            selectedRows={selectedRows}
            onRowSelect={handleRowSelect}
            onSelectAll={handleSelectAll}
          />
        </CardContent>
      </Card>

      {/* Pagination */}
      {totalPages > 1 && (
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-700">
              Page {uiState.currentPage} of {totalPages}
            </span>
            <select
              value={uiState.pageSize}
              onChange={(e) => onUpdatePagination(1, parseInt(e.target.value))}
              className="border border-gray-300 rounded-md px-3 py-1 text-sm"
            >
              <option value={25}>25 per page</option>
              <option value={50}>50 per page</option>
              <option value={100}>100 per page</option>
            </select>
          </div>
          
          <div className="flex space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => onUpdatePagination(uiState.currentPage - 1)}
              disabled={uiState.currentPage === 1}
            >
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => onUpdatePagination(uiState.currentPage + 1)}
              disabled={uiState.currentPage === totalPages}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </div>
  );
};
