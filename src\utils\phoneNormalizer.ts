/**
 * Phone number normalization utilities for msgclub DND API integration
 */

import logger from './logger';

export interface PhoneNormalizationResult {
  normalized: string;
  isValid: boolean;
  originalFormat: string;
  error?: string;
}

/**
 * Normalize phone number for msgclub API
 * msgclub expects numbers in format: 9677645051 (10-digit Indian mobile numbers)
 */
export function normalizePhoneNumber(phone: string): PhoneNormalizationResult {
  const originalFormat = phone;
  
  try {
    // Remove all non-digit characters
    let cleaned = phone.replace(/[^\d]/g, '');
    
    // Handle different input formats
    if (cleaned.startsWith('91') && cleaned.length === 12) {
      // Remove country code +91
      cleaned = cleaned.substring(2);
    } else if (cleaned.startsWith('0') && cleaned.length === 11) {
      // Remove leading 0 from Indian numbers
      cleaned = cleaned.substring(1);
    }
    
    // Validate Indian mobile number format
    if (cleaned.length === 10 && /^[6-9]\d{9}$/.test(cleaned)) {
      return {
        normalized: cleaned,
        isValid: true,
        originalFormat
      };
    }
    
    // If not a valid Indian mobile number, return as invalid
    return {
      normalized: cleaned,
      isValid: false,
      originalFormat,
      error: `Invalid Indian mobile number format: ${phone}`
    };
    
  } catch (error) {
    logger.error('Phone normalization error:', error);
    return {
      normalized: phone,
      isValid: false,
      originalFormat,
      error: `Phone normalization failed: ${(error as Error).message}`
    };
  }
}

/**
 * Normalize an array of phone numbers
 */
export function normalizePhoneNumbers(phones: string[]): {
  valid: string[];
  invalid: Array<{ phone: string; error: string }>;
} {
  const valid: string[] = [];
  const invalid: Array<{ phone: string; error: string }> = [];
  
  phones.forEach(phone => {
    const result = normalizePhoneNumber(phone);
    if (result.isValid) {
      valid.push(result.normalized);
    } else {
      invalid.push({
        phone: result.originalFormat,
        error: result.error || 'Invalid phone number format'
      });
    }
  });
  
  return { valid, invalid };
}

/**
 * Validate if a phone number is a valid Indian mobile number
 */
export function isValidIndianMobile(phone: string): boolean {
  const result = normalizePhoneNumber(phone);
  return result.isValid;
}

/**
 * Format phone number for display (add +91 prefix)
 */
export function formatPhoneForDisplay(phone: string): string {
  const result = normalizePhoneNumber(phone);
  if (result.isValid) {
    return `+91${result.normalized}`;
  }
  return phone; // Return original if invalid
}

/**
 * Batch phone numbers into chunks suitable for msgclub API
 * msgclub supports up to 100 numbers per request
 */
export function batchPhoneNumbers(phones: string[], batchSize: number = 100): string[][] {
  const batches: string[][] = [];
  
  for (let i = 0; i < phones.length; i += batchSize) {
    batches.push(phones.slice(i, i + batchSize));
  }
  
  return batches;
}

/**
 * Create comma-separated string for msgclub API
 */
export function createPhoneNumberString(phones: string[]): string {
  return phones.join(',');
}
