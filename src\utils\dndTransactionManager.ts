/**
 * @fileoverview Database Transaction Manager for DND Operations
 * @description Provides transaction support, retry logic, and connection pooling for DND persistence
 * <AUTHOR> System
 * @version 1.0.0
 */

import { db } from '../database/connection';
import { DNDErrorFactory, TransactionError, isRetryableError } from './dndPersistenceErrors';
import { createDNDPersistenceConfig } from '../config/dndPersistence';
import logger from './logger';

/**
 * Transaction context for database operations
 */
export interface TransactionContext {
  transactionId: string;
  startTime: number;
  operationCount: number;
  rollbackOnError: boolean;
}

/**
 * Retry configuration for database operations
 */
export interface RetryConfig {
  maxAttempts: number;
  baseDelayMs: number;
  maxDelayMs: number;
  exponentialBackoff: boolean;
  retryableErrors: string[];
}

/**
 * Database transaction manager with retry logic and connection pooling
 */
export class DNDTransactionManager {
  private readonly config: ReturnType<typeof createDNDPersistenceConfig>;
  private readonly retryConfig: RetryConfig;
  private activeTransactions: Map<string, TransactionContext> = new Map();

  /**
   * Creates a new transaction manager
   * @param {string} environment - Environment configuration
   */
  constructor(environment: string = process.env.NODE_ENV || 'development') {
    this.config = createDNDPersistenceConfig(environment);
    this.retryConfig = {
      maxAttempts: this.config.retryAttempts,
      baseDelayMs: this.config.retryDelayMs,
      maxDelayMs: this.config.retryDelayMs * 10,
      exponentialBackoff: true,
      retryableErrors: ['ECONNRESET', 'ETIMEDOUT', 'ENOTFOUND', 'ECONNREFUSED']
    };
  }

  /**
   * Executes a function within a database transaction with retry logic
   * @template T
   * @param {() => Promise<T>} operation - Operation to execute within transaction
   * @param {Partial<RetryConfig>} retryOptions - Retry configuration overrides
   * @returns {Promise<T>} Result of the operation
   * @throws {TransactionError} When transaction fails after all retries
   * 
   * @example
   * ```typescript
   * const manager = new DNDTransactionManager();
   * const result = await manager.executeWithTransaction(async () => {
   *   // Your database operations here
   *   return await someDbOperation();
   * });
   * ```
   */
  async executeWithTransaction<T>(
    operation: () => Promise<T>,
    retryOptions: Partial<RetryConfig> = {}
  ): Promise<T> {
    const finalRetryConfig = { ...this.retryConfig, ...retryOptions };
    const transactionId = this.generateTransactionId();
    
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= finalRetryConfig.maxAttempts; attempt++) {
      try {
        return await this.executeTransactionAttempt(operation, transactionId, attempt);
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        const shouldRetry = this.shouldRetryOperation(lastError, attempt, finalRetryConfig);
        
        if (!shouldRetry) {
          logger.error('Transaction failed - no more retries', {
            transactionId,
            attempt,
            error: lastError.message
          });
          break;
        }
        
        const delayMs = this.calculateRetryDelay(attempt, finalRetryConfig);
        
        logger.warn('Transaction failed - retrying', {
          transactionId,
          attempt,
          nextAttempt: attempt + 1,
          delayMs,
          error: lastError.message
        });
        
        await this.delay(delayMs);
      }
    }
    
    throw new TransactionError(
      'executeWithTransaction',
      lastError || new Error('Unknown transaction error')
    );
  }

  /**
   * Executes a single transaction attempt
   * @private
   * @template T
   * @param {() => Promise<T>} operation - Operation to execute
   * @param {string} transactionId - Transaction identifier
   * @param {number} attempt - Current attempt number
   * @returns {Promise<T>} Result of the operation
   */
  private async executeTransactionAttempt<T>(
    operation: () => Promise<T>,
    transactionId: string,
    attempt: number
  ): Promise<T> {
    const context: TransactionContext = {
      transactionId,
      startTime: Date.now(),
      operationCount: 0,
      rollbackOnError: true
    };
    
    this.activeTransactions.set(transactionId, context);
    
    try {
      logger.debug('Starting transaction', { transactionId, attempt });
      
      await db.query('BEGIN');
      context.operationCount++;
      
      const result = await operation();
      
      await db.query('COMMIT');
      context.operationCount++;
      
      const duration = Date.now() - context.startTime;
      logger.debug('Transaction completed successfully', {
        transactionId,
        attempt,
        duration,
        operationCount: context.operationCount
      });
      
      return result;
      
    } catch (error) {
      if (context.rollbackOnError) {
        try {
          await db.query('ROLLBACK');
          context.operationCount++;
          
          logger.debug('Transaction rolled back', { transactionId, attempt });
        } catch (rollbackError) {
          logger.error('Failed to rollback transaction', {
            transactionId,
            attempt,
            rollbackError: rollbackError instanceof Error ? rollbackError.message : String(rollbackError)
          });
        }
      }
      
      throw error;
    } finally {
      this.activeTransactions.delete(transactionId);
    }
  }

  /**
   * Executes an operation with retry logic (without transaction)
   * @template T
   * @param {() => Promise<T>} operation - Operation to execute
   * @param {string} operationName - Name of the operation for logging
   * @param {Partial<RetryConfig>} retryOptions - Retry configuration overrides
   * @returns {Promise<T>} Result of the operation
   * 
   * @example
   * ```typescript
   * const manager = new DNDTransactionManager();
   * const result = await manager.executeWithRetry(
   *   () => db.query('SELECT * FROM dnd_validations WHERE phone_number = $1', [phone]),
   *   'findByPhone'
   * );
   * ```
   */
  async executeWithRetry<T>(
    operation: () => Promise<T>,
    operationName: string,
    retryOptions: Partial<RetryConfig> = {}
  ): Promise<T> {
    const finalRetryConfig = { ...this.retryConfig, ...retryOptions };
    let lastError: Error | null = null;
    
    for (let attempt = 1; attempt <= finalRetryConfig.maxAttempts; attempt++) {
      try {
        const startTime = Date.now();
        const result = await operation();
        const duration = Date.now() - startTime;
        
        if (attempt > 1) {
          logger.info('Operation succeeded after retry', {
            operationName,
            attempt,
            duration
          });
        }
        
        return result;
        
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));
        
        const shouldRetry = this.shouldRetryOperation(lastError, attempt, finalRetryConfig);
        
        if (!shouldRetry) {
          logger.error('Operation failed - no more retries', {
            operationName,
            attempt,
            error: lastError.message
          });
          break;
        }
        
        const delayMs = this.calculateRetryDelay(attempt, finalRetryConfig);
        
        logger.warn('Operation failed - retrying', {
          operationName,
          attempt,
          nextAttempt: attempt + 1,
          delayMs,
          error: lastError.message
        });
        
        await this.delay(delayMs);
      }
    }
    
    throw DNDErrorFactory.fromDatabaseError(
      lastError || new Error('Unknown error'),
      operationName
    );
  }

  /**
   * Determines if an operation should be retried
   * @private
   * @param {Error} error - The error that occurred
   * @param {number} attempt - Current attempt number
   * @param {RetryConfig} config - Retry configuration
   * @returns {boolean} True if operation should be retried
   */
  private shouldRetryOperation(error: Error, attempt: number, config: RetryConfig): boolean {
    // Don't retry if we've reached max attempts
    if (attempt >= config.maxAttempts) {
      return false;
    }
    
    // Check if error is retryable using our error system
    if (isRetryableError(error)) {
      return true;
    }
    
    // Check for specific error codes/messages
    const errorMessage = error.message.toLowerCase();
    const errorCode = (error as any).code;
    
    return config.retryableErrors.some(retryableError => 
      errorMessage.includes(retryableError.toLowerCase()) ||
      errorCode === retryableError
    );
  }

  /**
   * Calculates retry delay with exponential backoff
   * @private
   * @param {number} attempt - Current attempt number
   * @param {RetryConfig} config - Retry configuration
   * @returns {number} Delay in milliseconds
   */
  private calculateRetryDelay(attempt: number, config: RetryConfig): number {
    if (!config.exponentialBackoff) {
      return config.baseDelayMs;
    }
    
    const exponentialDelay = config.baseDelayMs * Math.pow(2, attempt - 1);
    const jitteredDelay = exponentialDelay * (0.5 + Math.random() * 0.5); // Add jitter
    
    return Math.min(jitteredDelay, config.maxDelayMs);
  }

  /**
   * Delays execution for specified milliseconds
   * @private
   * @param {number} ms - Milliseconds to delay
   * @returns {Promise<void>}
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Generates a unique transaction ID
   * @private
   * @returns {string} Transaction ID
   */
  private generateTransactionId(): string {
    return `txn_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Gets information about active transactions
   * @returns {TransactionContext[]} Array of active transaction contexts
   */
  getActiveTransactions(): TransactionContext[] {
    return Array.from(this.activeTransactions.values());
  }

  /**
   * Gets transaction statistics
   * @returns {object} Transaction statistics
   */
  getTransactionStats(): {
    activeCount: number;
    totalProcessed: number;
    averageDuration: number;
  } {
    const active = this.activeTransactions.size;
    const contexts = Array.from(this.activeTransactions.values());
    const totalDuration = contexts.reduce((sum, ctx) => sum + (Date.now() - ctx.startTime), 0);
    const averageDuration = contexts.length > 0 ? totalDuration / contexts.length : 0;
    
    return {
      activeCount: active,
      totalProcessed: contexts.reduce((sum, ctx) => sum + ctx.operationCount, 0),
      averageDuration
    };
  }
}

/**
 * Singleton instance of transaction manager
 */
export const dndTransactionManager = new DNDTransactionManager();
