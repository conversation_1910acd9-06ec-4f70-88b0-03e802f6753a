import React, { useState, useEffect } from 'react';
import { ConversationCall } from '../components/ConversationCall';
import { WebCallDemo } from '../components/WebCallDemo';
import { Card } from '../components/ui/Card';
// import { Button } from '../components/ui/Button'; // Not used yet
import { Badge } from '../components/ui/Badge';
import { useLeads } from '../hooks/useLeads';
import { Lead } from '../types';
import { Phone, Users, Activity, Clock, Headphones } from 'lucide-react';

interface ActiveConversation {
  callSid: string;
  leadId: number;
  leadName: string;
  stage: string;
  duration: number;
  messageCount: number;
}

export const ConversationTestPage: React.FC = () => {
  const { leads, loading: leadsLoading } = useLeads();
  const [selectedLead, setSelectedLead] = useState<Lead | null>(null);
  const [activeConversations, setActiveConversations] = useState<ActiveConversation[]>([]);
  const [callHistory, setCallHistory] = useState<Array<{
    callSid: string;
    leadName: string;
    outcome: string;
    timestamp: Date;
  }>>([]);

  // Load active conversations on component mount
  useEffect(() => {
    loadActiveConversations();
    const interval = setInterval(loadActiveConversations, 5000); // Refresh every 5 seconds
    return () => clearInterval(interval);
  }, []);

  const loadActiveConversations = async () => {
    try {
      const response = await fetch('/api/v1/conversation/active');
      const data = await response.json();
      
      if (data.success) {
        setActiveConversations(data.conversations);
      }
    } catch (error) {
      console.error('Failed to load active conversations:', error);
    }
  };

  const handleCallStart = (callSid: string) => {
    console.log('Call started:', callSid);
    loadActiveConversations();
  };

  const handleCallEnd = (callSid: string, outcome: string) => {
    console.log('Call ended:', callSid, outcome);
    
    // Add to call history
    if (selectedLead) {
      setCallHistory(prev => [{
        callSid,
        leadName: selectedLead.name,
        outcome,
        timestamp: new Date()
      }, ...prev]);
    }

    // Refresh active conversations
    loadActiveConversations();
    
    // Clear selected lead
    setSelectedLead(null);
  };

  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const getStageColor = (stage: string): string => {
    const colors: Record<string, string> = {
      greeting: 'bg-blue-100 text-blue-800',
      discovery: 'bg-yellow-100 text-yellow-800',
      presentation: 'bg-purple-100 text-purple-800',
      objection_handling: 'bg-orange-100 text-orange-800',
      closing: 'bg-green-100 text-green-800',
      completed: 'bg-gray-100 text-gray-800'
    };
    return colors[stage] || 'bg-gray-100 text-gray-800';
  };

  if (leadsLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-gray-600">Loading leads...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">Conversation Testing</h1>
        <p className="text-gray-600">Test the AI conversation calling system with real leads and web calls</p>
      </div>

      {/* Web Call Demo Section */}
      <div className="mb-8">
        <WebCallDemo />
      </div>

      {/* Phone Call Testing Section */}
      <div className="mb-8">
        <h2 className="text-2xl font-semibold text-gray-900 mb-4 flex items-center">
          <Phone className="h-6 w-6 mr-2" />
          Phone Call Testing
        </h2>
        <p className="text-gray-600 mb-6">Test AI conversations through actual phone calls (requires Exotel KYC)</p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Lead Selection */}
        <div className="lg:col-span-1">
          <Card className="h-fit">
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Select Lead
              </h2>
              
              <div className="space-y-3 max-h-96 overflow-y-auto">
                {leads.map((lead) => (
                  <div
                    key={lead.id}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      selectedLead?.id === lead.id
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                    onClick={() => setSelectedLead(lead)}
                  >
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="font-medium">{lead.name}</h3>
                        <p className="text-sm text-gray-600">{lead.phone}</p>
                        {lead.notes && (
                          <p className="text-sm text-gray-500">{lead.notes}</p>
                        )}
                      </div>
                      <Badge 
                        className={
                          lead.status === 'new' ? 'bg-green-100 text-green-800' :
                          lead.status === 'contacted' ? 'bg-blue-100 text-blue-800' :
                          'bg-gray-100 text-gray-800'
                        }
                      >
                        {lead.status}
                      </Badge>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>

          {/* Active Conversations */}
          <Card className="mt-6">
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Activity className="h-5 w-5 mr-2" />
                Active Conversations ({activeConversations.length})
              </h2>
              
              {activeConversations.length === 0 ? (
                <p className="text-gray-500 text-sm">No active conversations</p>
              ) : (
                <div className="space-y-3">
                  {activeConversations.map((conv) => (
                    <div key={conv.callSid} className="p-3 border border-gray-200 rounded-lg">
                      <div className="flex items-center justify-between mb-2">
                        <h4 className="font-medium text-sm">{conv.leadName}</h4>
                        <Badge className={getStageColor(conv.stage)}>
                          {conv.stage.replace('_', ' ')}
                        </Badge>
                      </div>
                      <div className="flex items-center justify-between text-xs text-gray-600">
                        <span className="flex items-center">
                          <Clock className="h-3 w-3 mr-1" />
                          {formatDuration(conv.duration)}
                        </span>
                        <span>{conv.messageCount} messages</span>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>

          {/* Call History */}
          <Card className="mt-6">
            <div className="p-6">
              <h2 className="text-xl font-semibold mb-4 flex items-center">
                <Phone className="h-5 w-5 mr-2" />
                Recent Calls
              </h2>
              
              {callHistory.length === 0 ? (
                <p className="text-gray-500 text-sm">No recent calls</p>
              ) : (
                <div className="space-y-2">
                  {callHistory.slice(0, 5).map((call, index) => (
                    <div key={index} className="p-2 border border-gray-200 rounded text-sm">
                      <div className="flex items-center justify-between">
                        <span className="font-medium">{call.leadName}</span>
                        <Badge className={
                          call.outcome === 'completed' ? 'bg-green-100 text-green-800' :
                          'bg-gray-100 text-gray-800'
                        }>
                          {call.outcome}
                        </Badge>
                      </div>
                      <p className="text-xs text-gray-500 mt-1">
                        {call.timestamp.toLocaleString()}
                      </p>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* Conversation Interface */}
        <div className="lg:col-span-2">
          {selectedLead ? (
            <ConversationCall
              lead={selectedLead}
              onCallStart={handleCallStart}
              onCallEnd={handleCallEnd}
            />
          ) : (
            <Card className="h-96 flex items-center justify-center">
              <div className="text-center text-gray-500">
                <Phone className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-lg font-medium mb-2">Select a Lead to Start</h3>
                <p className="text-sm">Choose a lead from the list to begin a conversation call</p>
              </div>
            </Card>
          )}
        </div>
      </div>

      {/* Instructions */}
      <Card className="mt-8">
        <div className="p-6">
          <h2 className="text-xl font-semibold mb-4">How to Test</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Web Call Instructions */}
            <div>
              <h3 className="font-medium text-green-600 mb-3 flex items-center">
                <Headphones className="h-5 w-5 mr-2" />
                Web Call Demo (Recommended)
              </h3>
              <div className="space-y-2 text-sm text-gray-600">
                <p>• Click "Start Web Call" to test with real AI voice</p>
                <p>• Uses OpenAI STT/TTS for realistic experience</p>
                <p>• Perfect for client demonstrations</p>
                <p>• No phone required - works through browser</p>
              </div>
            </div>

            {/* Phone Call Instructions */}
            <div>
              <h3 className="font-medium text-blue-600 mb-3 flex items-center">
                <Phone className="h-5 w-5 mr-2" />
                Phone Call Testing
              </h3>
              <div className="space-y-2 text-sm text-gray-600">
                <p>• Select a lead and click "Start Call"</p>
                <p>• Requires Exotel KYC approval</p>
                <p>• Tests actual phone dialing</p>
                <p>• Type responses to simulate customer</p>
              </div>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
};
