import React, { useCallback, useState, useRef } from 'react';
import { Upload, FileText, AlertCircle, CheckCircle, X } from 'lucide-react';
import { Button } from '../ui/Button';
import { Card, CardContent } from '../ui/Card';
import { DNDUploadState, DNDValidationResponse } from '../../types';
import { formatFileSize } from '../../utils/csvProcessor';

interface DndUploadFormProps {
  uploadState: DNDUploadState;
  onUpload: (file: File) => Promise<DNDValidationResponse | null>;
  onValidate: (file: File) => Promise<{ isValid: boolean; errors: string[]; preview: any[] }>;
  onSuccess: (response: DNDValidationResponse) => void;
  onReset: () => void;
  onSetFile: (file: File) => void;
}

export const DndUploadForm: React.FC<DndUploadFormProps> = ({
  uploadState,
  onUpload,
  onValidate,
  onSuccess,
  onReset,
  onSetFile,
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [validation, setValidation] = useState<{
    isValid: boolean;
    errors: string[];
    preview: any[];
  } | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleDrag = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  }, []);

  const handleDrop = useCallback(async (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);

    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      const file = e.dataTransfer.files[0];
      await handleFileSelection(file);
    }
  }, []);

  const handleFileInput = useCallback(async (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      await handleFileSelection(file);
    }
  }, []);

  const handleFileSelection = useCallback(async (file: File) => {
    // Reset previous validation
    setValidation(null);

    // Set the file in upload state
    onSetFile(file);

    // Validate file
    const validationResult = await onValidate(file);
    setValidation(validationResult);
  }, [onValidate, onSetFile]);

  const handleUpload = useCallback(async () => {
    if (!uploadState.file) return;

    const response = await onUpload(uploadState.file);
    if (response) {
      onSuccess(response);
    }
  }, [uploadState.file, onUpload, onSuccess]);

  const handleReset = useCallback(() => {
    setValidation(null);
    onReset();
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [onReset]);

  const openFileDialog = useCallback(() => {
    fileInputRef.current?.click();
  }, []);

  return (
    <div className="space-y-6">
      {/* File Upload Area */}
      <div
        className={`relative border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          dragActive
            ? 'border-primary-500 bg-primary-50'
            : uploadState.file
            ? 'border-green-300 bg-green-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept=".csv"
          onChange={handleFileInput}
          className="hidden"
        />

        {uploadState.file ? (
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <FileText className="h-12 w-12 text-green-500" />
            </div>
            <div>
              <p className="text-lg font-medium text-gray-900">{uploadState.file.name}</p>
              <p className="text-sm text-gray-500">
                {formatFileSize(uploadState.file.size)} • CSV File
              </p>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleReset}
              icon={<X className="h-4 w-4" />}
            >
              Remove File
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-center">
              <Upload className="h-12 w-12 text-gray-400" />
            </div>
            <div>
              <p className="text-lg font-medium text-gray-900">
                Drop your CSV file here, or{' '}
                <button
                  type="button"
                  onClick={openFileDialog}
                  className="text-primary-600 hover:text-primary-500 underline"
                >
                  browse
                </button>
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Maximum file size: 50MB • Maximum records: 100,000
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Validation Results */}
      {validation && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                {validation.isValid ? (
                  <>
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="font-medium text-green-700">File validation passed</span>
                  </>
                ) : (
                  <>
                    <AlertCircle className="h-5 w-5 text-red-500" />
                    <span className="font-medium text-red-700">File validation failed</span>
                  </>
                )}
              </div>

              {!validation.isValid && validation.errors.length > 0 && (
                <div className="bg-red-50 border border-red-200 rounded-md p-4">
                  <h4 className="text-sm font-medium text-red-800 mb-2">Validation Errors:</h4>
                  <ul className="text-sm text-red-700 space-y-1">
                    {validation.errors.map((error, index) => (
                      <li key={index} className="flex items-start">
                        <span className="mr-2">•</span>
                        <span>{error}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {validation.isValid && validation.preview.length > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-md p-4">
                  <h4 className="text-sm font-medium text-green-800 mb-3">File Preview (First 5 rows):</h4>
                  <div className="overflow-x-auto">
                    <table className="min-w-full text-sm">
                      <thead>
                        <tr className="border-b border-green-200">
                          {Object.keys(validation.preview[0] || {}).map((header) => (
                            <th key={header} className="text-left py-2 px-3 font-medium text-green-800">
                              {header}
                            </th>
                          ))}
                        </tr>
                      </thead>
                      <tbody>
                        {validation.preview.slice(0, 5).map((row, index) => (
                          <tr key={index} className="border-b border-green-100">
                            {Object.values(row as any).map((value, cellIndex) => (
                              <td key={cellIndex} className="py-2 px-3 text-green-700">
                                {String(value)}
                              </td>
                            ))}
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload Progress */}
      {uploadState.isUploading && (
        <Card>
          <CardContent className="pt-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium text-gray-700">Uploading and processing...</span>
                <span className="text-sm text-gray-500">{uploadState.progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-primary-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${uploadState.progress}%` }}
                />
              </div>
              <p className="text-xs text-gray-500">
                This may take several minutes for large files. Please don't close this page.
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error Display */}
      {uploadState.error && (
        <Card>
          <CardContent className="pt-6">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex items-start">
                <AlertCircle className="h-5 w-5 text-red-500 mt-0.5 mr-3 flex-shrink-0" />
                <div>
                  <h4 className="text-sm font-medium text-red-800">Upload Failed</h4>
                  <p className="text-sm text-red-700 mt-1">{uploadState.error}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4">
        {uploadState.file && !uploadState.isUploading && (
          <Button
            variant="outline"
            onClick={handleReset}
          >
            Choose Different File
          </Button>
        )}
        
        <Button
          onClick={handleUpload}
          disabled={!validation?.isValid || uploadState.isUploading}
          loading={uploadState.isUploading}
        >
          {uploadState.isUploading ? 'Processing...' : 'Start DND Validation'}
        </Button>
      </div>
    </div>
  );
};
