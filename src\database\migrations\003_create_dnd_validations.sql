-- Create DND validations table for storing phone number validation results
-- Created: 2024-12-22

-- Create DND validations table
CREATE TABLE dnd_validations (
    phone_number VARCHAR(20) PRIMARY KEY,
    dnd_status VARCHAR(20) NOT NULL CHECK (dnd_status IN ('DND', 'Non-DND', 'Error')),
    validated_at TIMESTAMP WITH TIME ZONE NOT NULL,
    validation_metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add indexes for performance
CREATE INDEX idx_dnd_validations_dnd_status ON dnd_validations(dnd_status);
CREATE INDEX idx_dnd_validations_validated_at ON dnd_validations(validated_at);
CREATE INDEX idx_dnd_validations_created_at ON dnd_validations(created_at);

-- Add GIN index for JSONB validation_metadata column for efficient querying
CREATE INDEX idx_dnd_validations_metadata_gin ON dnd_validations USING GIN (validation_metadata);

-- Apply updated_at trigger to dnd_validations table
CREATE TRIGGER update_dnd_validations_updated_at BEFORE UPDATE ON dnd_validations
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Add comments for documentation
COMMENT ON TABLE dnd_validations IS 'Stores DND (Do Not Disturb) validation results for phone numbers';
COMMENT ON COLUMN dnd_validations.phone_number IS 'Normalized phone number (primary key)';
COMMENT ON COLUMN dnd_validations.dnd_status IS 'DND validation status: DND, Non-DND, or Error';
COMMENT ON COLUMN dnd_validations.validated_at IS 'Timestamp when the validation was performed';
COMMENT ON COLUMN dnd_validations.validation_metadata IS 'Additional metadata from DND validation API response';
COMMENT ON COLUMN dnd_validations.created_at IS 'Record creation timestamp';
COMMENT ON COLUMN dnd_validations.updated_at IS 'Record last update timestamp';
