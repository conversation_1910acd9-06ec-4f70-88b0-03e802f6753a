/**
 * @fileoverview Error Scenario Tests for DND Persistence
 * @description Comprehensive error handling and edge case testing
 */

import { DNDPersistenceService } from '@/services/dndPersistenceService';
import { DNDValidationModel } from '@/database/models/DNDValidation';
import { db } from '@/database/connection';
import { 
  InvalidPhoneNumberError,
  InvalidDNDStatusError,
  ValidationNotFoundError,
  DatabaseConnectionError,
  BulkSizeExceededError,
  isDNDPersistenceError
} from '@/utils/dndPersistenceErrors';

describe('DND Persistence Error Scenarios', () => {
  let persistenceService: DNDPersistenceService;

  beforeAll(async () => {
    await db.connect();
    persistenceService = new DNDPersistenceService();
  });

  afterAll(async () => {
    // Clean up test data
    await db.query('DELETE FROM dnd_validations WHERE phone_number LIKE $1', ['error%']);
    await db.disconnect();
  });

  describe('Input Validation Errors', () => {
    describe('Invalid Phone Numbers', () => {
      const invalidPhones = [
        '',
        '   ',
        null,
        undefined,
        '123',
        '12345678901234567890', // Too long
        'abcdefghij',
        '5123456789', // Invalid starting digit
        '+1234567890' // Non-Indian international
      ];

      invalidPhones.forEach(phone => {
        it(`should reject invalid phone number: ${phone}`, async () => {
          await expect(
            persistenceService.saveDNDValidation({
              phone: phone as any,
              dnd_status: 'DND'
            })
          ).rejects.toThrow(InvalidPhoneNumberError);
        });
      });

      it('should provide detailed error information for invalid phone', async () => {
        try {
          await persistenceService.saveDNDValidation({
            phone: 'invalid',
            dnd_status: 'DND'
          });
          fail('Should have thrown an error');
        } catch (error) {
          expect(isDNDPersistenceError(error)).toBe(true);
          expect(error).toBeInstanceOf(InvalidPhoneNumberError);
          expect(error.code).toBe('DND_INVALID_PHONE_NUMBER');
          expect(error.statusCode).toBe(400);
          expect(error.retryable).toBe(false);
        }
      });
    });

    describe('Invalid DND Status', () => {
      const invalidStatuses = [
        '',
        null,
        undefined,
        'INVALID',
        'dnd', // Case sensitive
        'yes',
        'no',
        123
      ];

      invalidStatuses.forEach(status => {
        it(`should reject invalid DND status: ${status}`, async () => {
          await expect(
            persistenceService.saveDNDValidation({
              phone: '9876543210',
              dnd_status: status as any
            })
          ).rejects.toThrow(InvalidDNDStatusError);
        });
      });
    });

    describe('Invalid Metadata', () => {
      it('should reject oversized metadata', async () => {
        const largeMetadata = {
          data: 'x'.repeat(20000) // Exceeds size limit
        };

        await expect(
          persistenceService.saveDNDValidation({
            phone: '9876543210',
            dnd_status: 'DND'
          }, {
            includeMetadata: true
          })
        ).rejects.toThrow();
      });

      it('should reject metadata with too many keys', async () => {
        const manyKeys: Record<string, any> = {};
        for (let i = 0; i < 100; i++) {
          manyKeys[`key${i}`] = `value${i}`;
        }

        // This would be caught during validation
        expect(() => {
          persistenceService['validator'].validateMetadata(manyKeys);
        }).not.toThrow(); // The validator should handle this gracefully
      });
    });
  });

  describe('Database Operation Errors', () => {
    describe('Connection Errors', () => {
      it('should handle database connection failures gracefully', async () => {
        // Mock database connection failure
        const originalQuery = db.query;
        db.query = jest.fn().mockRejectedValue(new Error('Connection failed'));

        try {
          await expect(
            persistenceService.saveDNDValidation({
              phone: '9876543210',
              dnd_status: 'DND'
            })
          ).rejects.toThrow();
        } finally {
          // Restore original query method
          db.query = originalQuery;
        }
      });

      it('should retry on retryable database errors', async () => {
        let attemptCount = 0;
        const originalQuery = db.query;
        
        db.query = jest.fn().mockImplementation((...args) => {
          attemptCount++;
          if (attemptCount < 3) {
            const error = new Error('Connection timeout');
            (error as any).code = 'ETIMEDOUT';
            throw error;
          }
          return originalQuery.apply(db, args);
        });

        try {
          await persistenceService.saveDNDValidation({
            phone: 'error9876543210',
            dnd_status: 'DND'
          });
          
          expect(attemptCount).toBeGreaterThan(1); // Should have retried
        } finally {
          db.query = originalQuery;
        }
      });
    });

    describe('Query Timeout Errors', () => {
      it('should handle query timeouts appropriately', async () => {
        const originalQuery = db.query;
        db.query = jest.fn().mockImplementation(() => {
          return new Promise((_, reject) => {
            setTimeout(() => {
              const error = new Error('Query timeout');
              (error as any).code = 'QUERY_TIMEOUT';
              reject(error);
            }, 100);
          });
        });

        try {
          await expect(
            persistenceService.saveDNDValidation({
              phone: '9876543210',
              dnd_status: 'DND'
            })
          ).rejects.toThrow();
        } finally {
          db.query = originalQuery;
        }
      });
    });

    describe('Transaction Errors', () => {
      it('should handle transaction rollback scenarios', async () => {
        // This would require more complex mocking of transaction behavior
        // For now, we'll test that the service handles transaction errors
        const originalQuery = db.query;
        let queryCount = 0;
        
        db.query = jest.fn().mockImplementation((query, params) => {
          queryCount++;
          if (query.includes('BEGIN')) {
            return Promise.resolve({ rows: [], rowCount: 0 });
          }
          if (queryCount === 2) { // Fail on the actual operation
            throw new Error('Transaction failed');
          }
          return originalQuery.call(db, query, params);
        });

        try {
          await expect(
            persistenceService.saveDNDValidation({
              phone: '9876543210',
              dnd_status: 'DND'
            })
          ).rejects.toThrow();
        } finally {
          db.query = originalQuery;
        }
      });
    });
  });

  describe('Business Logic Errors', () => {
    describe('Not Found Errors', () => {
      it('should throw ValidationNotFoundError for non-existent records', async () => {
        await expect(
          persistenceService.deleteDNDValidation('nonexistent123')
        ).rejects.toThrow(ValidationNotFoundError);
      });

      it('should return null for non-existent lookups', async () => {
        const result = await persistenceService.getDNDValidation('nonexistent456');
        expect(result).toBeNull();
      });
    });

    describe('Bulk Operation Errors', () => {
      it('should handle bulk size exceeded errors', async () => {
        const oversizedBulk = Array.from({ length: 10000 }, (_, i) => ({
          phone: `error${i.toString().padStart(10, '0')}`,
          dnd_status: 'DND' as const
        }));

        // This should be caught by validation before reaching the database
        const validator = persistenceService['validator'];
        const result = validator.validateBulkSize(oversizedBulk.length, 1000);
        expect(result.isValid).toBe(false);
      });

      it('should handle partial bulk operation failures', async () => {
        const mixedData = [
          { phone: 'error9876543210', dnd_status: 'DND' as const },
          { phone: 'invalid_phone', dnd_status: 'DND' as const }, // This will be skipped
          { phone: 'error9876543211', dnd_status: 'Non-DND' as const }
        ];

        // The service should skip invalid entries and process valid ones
        const result = await persistenceService.saveBulkDNDValidations(mixedData);
        expect(result.length).toBeLessThan(mixedData.length); // Some entries skipped
      });
    });
  });

  describe('Concurrency and Race Condition Errors', () => {
    it('should handle concurrent updates to the same record', async () => {
      const phoneNumber = 'error9876543220';
      
      // Create initial record
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'DND'
      });

      // Simulate concurrent updates
      const updates = [
        persistenceService.saveDNDValidation({
          phone: phoneNumber,
          dnd_status: 'Non-DND'
        }),
        persistenceService.saveDNDValidation({
          phone: phoneNumber,
          dnd_status: 'Error'
        })
      ];

      // Both should succeed due to upsert behavior
      const results = await Promise.all(updates);
      expect(results).toHaveLength(2);
      
      // Final state should be one of the updates
      const final = await persistenceService.getDNDValidation(phoneNumber);
      expect(['Non-DND', 'Error']).toContain(final?.dndStatus);
    });

    it('should handle concurrent bulk operations', async () => {
      const batch1 = Array.from({ length: 10 }, (_, i) => ({
        phone: `errorbatch1${i.toString().padStart(4, '0')}`,
        dnd_status: 'DND' as const
      }));

      const batch2 = Array.from({ length: 10 }, (_, i) => ({
        phone: `errorbatch2${i.toString().padStart(4, '0')}`,
        dnd_status: 'Non-DND' as const
      }));

      // Run concurrent bulk operations
      const [result1, result2] = await Promise.all([
        persistenceService.saveBulkDNDValidations(batch1),
        persistenceService.saveBulkDNDValidations(batch2)
      ]);

      expect(result1).toHaveLength(10);
      expect(result2).toHaveLength(10);
    });
  });

  describe('Resource Exhaustion Scenarios', () => {
    it('should handle memory pressure gracefully', async () => {
      // Create a large number of operations to stress test memory usage
      const operations = Array.from({ length: 100 }, (_, i) => 
        persistenceService.saveDNDValidation({
          phone: `errormem${i.toString().padStart(6, '0')}`,
          dnd_status: 'DND'
        })
      );

      // This should complete without memory errors
      await expect(Promise.all(operations)).resolves.toHaveLength(100);
    });

    it('should handle connection pool exhaustion', async () => {
      // Create many concurrent operations to test connection pooling
      const concurrentOps = 50;
      const operations = Array.from({ length: concurrentOps }, (_, i) => 
        persistenceService.getDNDValidation(`nonexistent${i}`)
      );

      // Should handle gracefully even if connection pool is stressed
      await expect(Promise.all(operations)).resolves.toHaveLength(concurrentOps);
    });
  });

  describe('Data Integrity Errors', () => {
    it('should handle corrupted data gracefully', async () => {
      // Insert corrupted data directly into database
      await db.query(`
        INSERT INTO dnd_validations (phone_number, dnd_status, validated_at, validation_metadata)
        VALUES ($1, $2, $3, $4)
      `, ['errorcorrupt001', 'INVALID_STATUS', new Date(), '{"corrupted": true}']);

      // Service should handle this gracefully when retrieving
      const result = await persistenceService.getDNDValidation('errorcorrupt001');
      expect(result).not.toBeNull();
      expect(result?.dndStatus).toBe('INVALID_STATUS'); // Should return as-is
    });

    it('should validate data consistency', async () => {
      // Test that upsert maintains data consistency
      const phoneNumber = 'errorcons001';
      
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'DND'
      });

      const before = await persistenceService.getDNDValidation(phoneNumber);
      
      await persistenceService.saveDNDValidation({
        phone: phoneNumber,
        dnd_status: 'Non-DND'
      });

      const after = await persistenceService.getDNDValidation(phoneNumber);
      
      expect(before?.createdAt).toEqual(after?.createdAt); // Created date should remain same
      expect(after?.updatedAt.getTime()).toBeGreaterThan(before?.updatedAt.getTime() || 0); // Updated date should change
    });
  });

  describe('Error Recovery and Resilience', () => {
    it('should recover from temporary database failures', async () => {
      let failureCount = 0;
      const originalQuery = db.query;
      
      db.query = jest.fn().mockImplementation((...args) => {
        failureCount++;
        if (failureCount <= 2) {
          throw new Error('Temporary failure');
        }
        return originalQuery.apply(db, args);
      });

      try {
        // Should eventually succeed after retries
        await persistenceService.saveDNDValidation({
          phone: 'errorrecovery001',
          dnd_status: 'DND'
        });
        
        expect(failureCount).toBeGreaterThan(1);
      } finally {
        db.query = originalQuery;
      }
    });

    it('should maintain service availability during partial failures', async () => {
      // Even if some operations fail, others should continue to work
      const operations = [
        persistenceService.saveDNDValidation({
          phone: 'errorpartial001',
          dnd_status: 'DND'
        }),
        persistenceService.saveDNDValidation({
          phone: 'invalid_phone', // This will fail
          dnd_status: 'DND'
        }).catch(() => null), // Catch and ignore this failure
        persistenceService.saveDNDValidation({
          phone: 'errorpartial002',
          dnd_status: 'Non-DND'
        })
      ];

      const results = await Promise.all(operations);
      
      // Two operations should succeed, one should fail
      const successCount = results.filter(r => r !== null).length;
      expect(successCount).toBe(2);
    });
  });

  describe('Error Logging and Monitoring', () => {
    it('should log errors with appropriate detail', async () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      try {
        await persistenceService.saveDNDValidation({
          phone: 'invalid',
          dnd_status: 'DND'
        });
      } catch (error) {
        // Error should be logged
        // Note: Actual logging verification would depend on your logging implementation
      }
      
      consoleSpy.mockRestore();
    });

    it('should provide structured error information', async () => {
      try {
        await persistenceService.saveDNDValidation({
          phone: '',
          dnd_status: 'DND'
        });
        fail('Should have thrown an error');
      } catch (error) {
        expect(isDNDPersistenceError(error)).toBe(true);
        
        const errorJson = error.toJSON();
        expect(errorJson).toHaveProperty('name');
        expect(errorJson).toHaveProperty('message');
        expect(errorJson).toHaveProperty('code');
        expect(errorJson).toHaveProperty('statusCode');
        expect(errorJson).toHaveProperty('timestamp');
        expect(errorJson).toHaveProperty('retryable');
      }
    });
  });
});
