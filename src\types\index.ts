// Core entity types
export interface User {
  id: number;
  email: string;
  name: string;
  role: 'admin' | 'user';
  createdAt: Date;
  updatedAt: Date;
}

export interface Lead {
  id: number;
  name: string;
  phone: string;
  email?: string;
  status: 'new' | 'contacted' | 'qualified' | 'converted' | 'rejected';
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface Call {
  id: number;
  leadId: number;
  twilioCallSid?: string;
  status: 'initiated' | 'ringing' | 'in-progress' | 'completed' | 'failed' | 'no-answer';
  duration?: number; // in seconds
  recordingUrl?: string;
  transcript?: string;
  aiResponse?: string;
  startedAt?: Date;
  endedAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

// API Request/Response types
export interface CreateLeadRequest {
  name: string;
  phone: string;
  email?: string;
  notes?: string;
}

export interface UpdateLeadRequest {
  name?: string;
  phone?: string;
  email?: string;
  status?: Lead['status'];
  notes?: string;
}

export interface CreateUserRequest {
  email: string;
  name: string;
  password: string;
  role?: User['role'];
}

export interface UpdateUserRequest {
  email?: string;
  name?: string;
  password?: string;
  role?: User['role'];
}

export interface InitiateCallRequest {
  leadId: number;
  message?: string;
}

export interface AuthRequest {
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  user: Omit<User, 'password'>;
}

// Database types
export interface QueryResult {
  rows: any[];
  rowCount: number;
}

// Service interfaces
export interface DatabaseService {
  connect(): Promise<void>;
  disconnect(): Promise<void>;
  query(text: string, params?: any[]): Promise<QueryResult>;
  transaction<T>(callback: (query: (sql: string, params?: any[]) => Promise<QueryResult>) => Promise<T>): Promise<T>;
  healthCheck(): Promise<boolean>;
}

export interface TelephonyService {
  initiateCall(phoneNumber: string, message: string): Promise<string>;
  getCallStatus(callSid: string): Promise<string>;
  getRecording(callSid: string): Promise<string | null>;
  validatePhoneNumber(phone: string): boolean;
  endCall?(callSid: string): Promise<void>;
}

export interface AIService {
  processVoiceInput(audioData: Buffer): Promise<string>;
  generateResponse(transcript: string, context: string): Promise<string>;
  synthesizeSpeech(text: string): Promise<Buffer>;
}

// Configuration types
export interface Config {
  port: number;
  nodeEnv: string;
  database: {
    url: string;
    host: string;
    port: number;
    name: string;
    user: string;
    password: string;
  };
  jwt: {
    secret: string;
    expiresIn: string;
  };
  twilio: {
    accountSid: string;
    authToken: string;
    phoneNumber: string;
  };
  litellm: {
    baseUrl: string;
    apiKey: string;
  };
  openai?: {
    apiKey: string;
  };
}

// Error types
export interface ApiError extends Error {
  statusCode: number;
  code?: string;
}

// Express middleware types
export interface AuthenticatedRequest extends Express.Request {
  user?: User;
}

// Webhook types
export interface TwilioWebhookRequest {
  CallSid: string;
  CallStatus: string;
  From: string;
  To: string;
  Duration?: string;
  RecordingUrl?: string;
}

// DND Validation types
export interface DNDValidation {
  phoneNumber: string;
  dndStatus: 'DND' | 'Non-DND' | 'Error';
  validatedAt: Date;
  validationMetadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface CreateDNDValidationRequest {
  phoneNumber: string;
  dndStatus: 'DND' | 'Non-DND' | 'Error';
  validatedAt?: Date;
  validationMetadata?: Record<string, any>;
}

export interface UpdateDNDValidationRequest {
  dndStatus?: 'DND' | 'Non-DND' | 'Error';
  validatedAt?: Date;
  validationMetadata?: Record<string, any>;
}

export interface DNDValidationStats {
  totalValidations: number;
  dndCount: number;
  nonDndCount: number;
  errorCount: number;
  lastValidatedAt?: Date;
}

// Test types
export interface TestContext {
  app: Express.Application;
  db: DatabaseService;
  cleanup: () => Promise<void>;
}
