import { LiteLLMService } from '../ai/litellm-service';
import { LeadModel } from '../../database/models/Lead';
import logger from '../../utils/logger';

export interface ConversationContext {
  callSid: string;
  leadId?: number | undefined;
  agentId?: number | undefined;
  sessionData: Map<string, any>;
  conversationHistory: Array<{
    role: 'user' | 'assistant' | 'system';
    content: string;
    timestamp: Date;
  }>;
  currentIntent?: string | undefined;
  extractedData: Map<string, any>;
  flowState: string;
}

export interface ConversationConfig {
  maxHistoryLength: number;
  defaultPersonality: string;
  responseTimeout: number;
  enableIntentDetection: boolean;
  enableDataExtraction: boolean;
}

export class ConversationEngine {
  private litellm: LiteLLMService;
  private config: ConversationConfig;
  private activeSessions: Map<string, ConversationContext> = new Map();

  constructor(litellm: LiteLLMService, config: ConversationConfig) {
    this.litellm = litellm;
    this.config = config;
    logger.info('🤖 Conversation engine initialized');
  }

  async initializeSession(callSid: string, leadId?: number): Promise<void> {
    try {
      logger.info(`🎯 Initializing conversation session for call: ${callSid}`);

      const context: ConversationContext = {
        callSid,
        leadId,
        sessionData: new Map(),
        conversationHistory: [],
        extractedData: new Map(),
        flowState: 'greeting'
      };

      // Load lead information if available
      if (leadId) {
        const lead = await LeadModel.findById(leadId);
        if (lead) {
          context.sessionData.set('lead', lead);
          context.sessionData.set('leadName', lead.name);
          context.sessionData.set('leadPhone', lead.phone);
          context.sessionData.set('leadEmail', lead.email);
          context.sessionData.set('leadCompany', (lead as any).company || 'Unknown');
        }
      }

      // Set system prompt based on lead context
      const systemPrompt = await this.generateSystemPrompt(context);
      context.conversationHistory.push({
        role: 'system',
        content: systemPrompt,
        timestamp: new Date()
      });

      this.activeSessions.set(callSid, context);
      logger.info(`✅ Conversation session initialized for call: ${callSid}`);

    } catch (error) {
      logger.error('❌ Error initializing conversation session:', error);
      throw error;
    }
  }

  async processInput(
    callSid: string,
    userInput: string,
    sessionContext?: any
  ): Promise<string | null> {
    const context = this.activeSessions.get(callSid);
    if (!context) {
      logger.error(`❌ No conversation context found for call: ${callSid}`);
      return "I'm sorry, there seems to be a technical issue. Let me transfer you to a human agent.";
    }

    try {
      logger.info(`🎤 Processing input for call ${callSid}: "${userInput}"`);

      // Add user input to conversation history
      context.conversationHistory.push({
        role: 'user',
        content: userInput,
        timestamp: new Date()
      });

      // Detect intent if enabled
      if (this.config.enableIntentDetection) {
        context.currentIntent = await this.detectIntent(userInput, context);
      }

      // Extract data if enabled
      if (this.config.enableDataExtraction) {
        await this.extractData(userInput, context);
      }

      // Update flow state based on intent and current state
      await this.updateFlowState(context);

      // Generate AI response
      const response = await this.generateResponse(context);

      if (response) {
        // Add AI response to conversation history
        context.conversationHistory.push({
          role: 'assistant',
          content: response,
          timestamp: new Date()
        });

        // Trim conversation history if too long
        this.trimConversationHistory(context);

        logger.info(`🤖 Generated response for call ${callSid}: "${response}"`);
        return response;
      }

      return null;

    } catch (error) {
      logger.error('❌ Error processing conversation input:', error);
      return "I apologize, but I'm having trouble understanding. Could you please repeat that?";
    }
  }

  async generatePersonalizedGreeting(lead: any): Promise<string> {
    try {
      // For web demo, use a simple, professional greeting without placeholders
      if (lead.name === 'Web Demo User') {
        return "Hello! This is Alex from TeleAI. I'm calling to demonstrate our AI-powered conversation system. How are you doing today?";
      }

      const prompt = `Generate a personalized greeting for a sales call to:
Name: ${lead.name}
Company: ${lead.company || 'their company'}
Phone: ${lead.phone}

The greeting should be:
- Professional and friendly
- Brief (1-2 sentences)
- Acknowledge them by name
- Mention their company if available
- Ask how you can help them
- NO placeholders like [Your Name] or [Your Company]
- Use "Alex from TeleAI" as the caller identity

Example: "Hello ${lead.name}! This is Alex from TeleAI calling about your inquiry regarding our services for ${lead.company}. How can I help you today?"

Generate a similar personalized greeting:`;

      const response = await this.litellm.generateResponse([
        { role: 'user', content: prompt }
      ]);

      return response || `Hello ${lead.name}! This is Alex from TeleAI calling about your inquiry. How can I help you today?`;

    } catch (error) {
      logger.error('❌ Error generating personalized greeting:', error);
      return `Hello ${lead.name}! This is Alex from TeleAI. How can I help you today?`;
    }
  }

  private async generateSystemPrompt(context: ConversationContext): Promise<string> {
    const lead = context.sessionData.get('lead');
    
    let systemPrompt = `You are a professional AI sales assistant making a phone call. Your goal is to:

1. Be conversational, friendly, and professional
2. Listen actively to the customer's needs
3. Provide helpful information about our services
4. Qualify leads and gather contact information
5. Schedule follow-up meetings when appropriate
6. Handle objections gracefully
7. Know when to transfer to a human agent

IMPORTANT GUIDELINES:
- Keep responses concise (1-3 sentences max)
- Speak naturally as if in a phone conversation
- Ask one question at a time
- Be empathetic and understanding
- If customer asks to speak to a human, agree immediately
- If you don't know something, admit it and offer to find out

CONVERSATION FLOW:
1. Greeting - Introduce yourself and purpose
2. Discovery - Ask about their needs and challenges
3. Presentation - Share relevant solutions
4. Handling objections - Address concerns
5. Closing - Schedule next steps or transfer to human

`;

    if (lead) {
      systemPrompt += `
LEAD INFORMATION:
- Name: ${lead.name}
- Company: ${lead.company || 'Not specified'}
- Phone: ${lead.phone}
- Email: ${lead.email || 'Not provided'}
- Source: ${lead.source || 'Unknown'}
- Notes: ${lead.notes || 'None'}

Use this information to personalize the conversation.`;
    }

    return systemPrompt;
  }

  private async detectIntent(userInput: string, context: ConversationContext): Promise<string> {
    try {
      const prompt = `Analyze this customer response and identify the primary intent:

Customer said: "${userInput}"

Possible intents:
- interested: Customer shows interest in the product/service
- not_interested: Customer explicitly says they're not interested
- request_info: Customer wants more information
- pricing_question: Customer asks about pricing
- schedule_meeting: Customer wants to schedule a call/meeting
- speak_to_human: Customer wants to talk to a human agent
- objection: Customer raises concerns or objections
- positive_response: Customer agrees or shows positive sentiment
- negative_response: Customer disagrees or shows negative sentiment
- clarification: Customer asks for clarification or to repeat something
- other: None of the above

Respond with just the intent name:`;

      const response = await this.litellm.generateResponse([
        { role: 'user', content: prompt }
      ]);

      return response?.toLowerCase().trim() || 'other';

    } catch (error) {
      logger.error('❌ Error detecting intent:', error);
      return 'other';
    }
  }

  private async extractData(userInput: string, context: ConversationContext): Promise<void> {
    try {
      const prompt = `Extract any relevant business information from this customer response:

Customer said: "${userInput}"

Look for and extract:
- Company name
- Industry
- Number of employees
- Budget range
- Timeline
- Specific needs or pain points
- Contact information (email, phone)
- Decision maker role

Return as JSON with only the fields that were mentioned:
Example: {"company": "ABC Corp", "industry": "manufacturing", "employees": "50-100"}

If no relevant data is found, return: {}`;

      const response = await this.litellm.generateResponse([
        { role: 'user', content: prompt }
      ]);

      if (response) {
        try {
          const extractedData = JSON.parse(response);
          for (const [key, value] of Object.entries(extractedData)) {
            context.extractedData.set(key, value);
          }
        } catch (parseError) {
          logger.warn('❌ Could not parse extracted data JSON:', parseError);
        }
      }

    } catch (error) {
      logger.error('❌ Error extracting data:', error);
    }
  }

  private async updateFlowState(context: ConversationContext): Promise<void> {
    const intent = context.currentIntent;
    const currentState = context.flowState;

    // Simple state machine for conversation flow
    switch (currentState) {
      case 'greeting':
        if (intent === 'interested' || intent === 'positive_response') {
          context.flowState = 'discovery';
        } else if (intent === 'not_interested') {
          context.flowState = 'objection_handling';
        } else if (intent === 'speak_to_human') {
          context.flowState = 'transfer_request';
        }
        break;

      case 'discovery':
        if (intent === 'request_info' || intent === 'pricing_question') {
          context.flowState = 'presentation';
        } else if (intent === 'schedule_meeting') {
          context.flowState = 'closing';
        } else if (intent === 'objection') {
          context.flowState = 'objection_handling';
        }
        break;

      case 'presentation':
        if (intent === 'interested' || intent === 'positive_response') {
          context.flowState = 'closing';
        } else if (intent === 'objection' || intent === 'negative_response') {
          context.flowState = 'objection_handling';
        }
        break;

      case 'objection_handling':
        if (intent === 'positive_response' || intent === 'interested') {
          context.flowState = 'discovery';
        } else if (intent === 'not_interested') {
          context.flowState = 'polite_close';
        }
        break;
    }

    logger.debug(`🔄 Flow state updated: ${currentState} → ${context.flowState} (intent: ${intent})`);
  }

  private async generateResponse(context: ConversationContext): Promise<string | null> {
    try {
      // Prepare conversation history for AI
      const messages = context.conversationHistory.map(entry => ({
        role: entry.role,
        content: entry.content
      }));

      // Add flow state context
      const flowContext = this.getFlowContextPrompt(context);
      if (flowContext) {
        messages.push({
          role: 'system',
          content: flowContext
        });
      }

      const response = await this.litellm.generateResponse(messages);
      return response;

    } catch (error) {
      logger.error('❌ Error generating AI response:', error);
      return null;
    }
  }

  private getFlowContextPrompt(context: ConversationContext): string {
    const state = context.flowState;
    const intent = context.currentIntent;

    switch (state) {
      case 'greeting':
        return "You're in the greeting phase. Be warm and professional. Introduce yourself and ask how you can help.";

      case 'discovery':
        return "You're in the discovery phase. Ask open-ended questions to understand their business needs and challenges.";

      case 'presentation':
        return "You're in the presentation phase. Share relevant information about how your services can help solve their specific needs.";

      case 'objection_handling':
        return "You're handling objections. Be empathetic, acknowledge their concerns, and provide reassuring information.";

      case 'closing':
        return "You're in the closing phase. Try to schedule a follow-up meeting or next steps.";

      case 'transfer_request':
        return "The customer wants to speak to a human. Acknowledge their request and confirm the transfer.";

      case 'polite_close':
        return "End the conversation politely. Thank them for their time and leave the door open for future contact.";

      default:
        return "";
    }
  }

  private trimConversationHistory(context: ConversationContext): void {
    if (context.conversationHistory.length > this.config.maxHistoryLength) {
      // Keep system prompt and trim from the middle
      const systemPrompt = context.conversationHistory[0];
      if (systemPrompt) {
        const recentMessages = context.conversationHistory.slice(-this.config.maxHistoryLength + 1);
        context.conversationHistory = [systemPrompt, ...recentMessages];
      }
    }
  }

  async handleDTMF(callSid: string, digit: string): Promise<void> {
    const context = this.activeSessions.get(callSid);
    if (!context) return;

    // Store DTMF input for potential use
    context.sessionData.set('lastDTMF', digit);
    context.sessionData.set('dtmfTimestamp', new Date());

    logger.info(`📞 DTMF ${digit} handled for call: ${callSid}`);
  }

  async handleFlowMark(callSid: string, mark: string): Promise<void> {
    const context = this.activeSessions.get(callSid);
    if (!context) return;

    // Handle custom flow marks
    if (mark.startsWith('flow:')) {
      const newState = mark.substring(5);
      context.flowState = newState;
      logger.info(`📍 Flow state changed to: ${newState} for call: ${callSid}`);
    }
  }

  async clearContext(callSid: string): Promise<void> {
    const context = this.activeSessions.get(callSid);
    if (!context) return;

    // Clear conversation history but keep system prompt and lead data
    const systemPrompt = context.conversationHistory[0];
    if (systemPrompt) {
      context.conversationHistory = [systemPrompt];
    } else {
      context.conversationHistory = [];
    }
    context.extractedData.clear();
    context.flowState = 'greeting';
    context.currentIntent = undefined;

    logger.info(`🔄 Context cleared for call: ${callSid}`);
  }

  async endSession(callSid: string): Promise<void> {
    const context = this.activeSessions.get(callSid);
    if (!context) return;

    try {
      // Save conversation data if needed
      logger.info(`📊 Conversation summary for call ${callSid}:`);
      logger.info(`- Turns: ${context.conversationHistory.length}`);
      logger.info(`- Final state: ${context.flowState}`);
      logger.info(`- Intent: ${context.currentIntent}`);
      logger.info(`- Extracted data: ${JSON.stringify(Object.fromEntries(context.extractedData))}`);

      this.activeSessions.delete(callSid);
      logger.info(`✅ Conversation session ended for call: ${callSid}`);

    } catch (error) {
      logger.error('❌ Error ending conversation session:', error);
    }
  }

  // Monitoring methods
  getActiveSessionCount(): number {
    return this.activeSessions.size;
  }

  getSessionInfo(callSid: string): ConversationContext | undefined {
    return this.activeSessions.get(callSid);
  }
}
